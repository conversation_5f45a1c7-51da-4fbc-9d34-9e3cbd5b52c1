"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderPlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _SyncStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SyncStatus */ \"(app-pages-browser)/./src/components/SyncStatus.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { conversations, workspaces, currentConversationId, currentWorkspaceId, isOpen, onToggle, onNewChat, onSelectConversation, onDeleteConversation, onRenameConversation, onSelectWorkspace, onCreateWorkspace, onUpdateWorkspace, onDeleteWorkspace, onReorderWorkspaces, onAssignConversationToWorkspace, onOpenSettings } = param;\n    _s();\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hoveredId, setHoveredId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedWorkspaces, setExpandedWorkspaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showWorkspaceForm, setShowWorkspaceForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingWorkspaceId, setEditingWorkspaceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConversationMenu, setShowConversationMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedWorkspace, setDraggedWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverWorkspace, setDragOverWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredWorkspace, setHoveredWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedConversation, setDraggedConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverTarget, setDragOverTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [workspaceFormData, setWorkspaceFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#6366f1'\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    // Close conversation menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": ()=>{\n                    setShowConversationMenu(null);\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            if (showConversationMenu) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"Sidebar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"Sidebar.useEffect\"];\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        showConversationMenu\n    ]);\n    const handleStartEdit = (conversation)=>{\n        setEditingId(conversation.id);\n        setEditTitle(conversation.title);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingId && editTitle.trim()) {\n            onRenameConversation(editingId, editTitle.trim());\n        }\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const handleCancelEdit = ()=>{\n        setEditingId(null);\n        setEditTitle('');\n    };\n    // Workspace helper functions\n    const toggleWorkspace = (workspaceId)=>{\n        const newExpanded = new Set(expandedWorkspaces);\n        if (newExpanded.has(workspaceId)) {\n            newExpanded.delete(workspaceId);\n        } else {\n            newExpanded.add(workspaceId);\n        }\n        setExpandedWorkspaces(newExpanded);\n    };\n    const handleCreateWorkspace = ()=>{\n        if (workspaceFormData.name.trim()) {\n            onCreateWorkspace({\n                name: workspaceFormData.name.trim(),\n                description: workspaceFormData.description.trim() || undefined,\n                color: workspaceFormData.color\n            });\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n            setShowWorkspaceForm(false);\n        }\n    };\n    const handleUpdateWorkspace = ()=>{\n        if (editingWorkspaceId && workspaceFormData.name.trim()) {\n            const workspace = workspaces.find((w)=>w.id === editingWorkspaceId);\n            if (workspace) {\n                onUpdateWorkspace({\n                    ...workspace,\n                    name: workspaceFormData.name.trim(),\n                    description: workspaceFormData.description.trim() || undefined,\n                    color: workspaceFormData.color\n                });\n            }\n            setEditingWorkspaceId(null);\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n        }\n    };\n    const startEditWorkspace = (workspace)=>{\n        setEditingWorkspaceId(workspace.id);\n        setWorkspaceFormData({\n            name: workspace.name,\n            description: workspace.description || '',\n            color: workspace.color\n        });\n    };\n    const handleDeleteWorkspace = (workspaceId)=>{\n        if (confirm('Are you sure you want to delete this workspace? Conversations will be moved to unassigned.')) {\n            onDeleteWorkspace(workspaceId);\n        }\n    };\n    const handleCreateConversationInWorkspace = (workspaceId)=>{\n        // Create a new conversation and assign it to the workspace\n        onNewChat(workspaceId);\n    };\n    const handleAssignConversation = (conversationId, workspaceId)=>{\n        onAssignConversationToWorkspace(conversationId, workspaceId);\n        setShowConversationMenu(null);\n    };\n    // Drag and drop handlers for workspace reordering\n    const handleDragStart = (e, workspaceId)=>{\n        setDraggedWorkspace(workspaceId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', workspaceId);\n    };\n    const handleDragOver = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        e.dataTransfer.dropEffect = 'move';\n        if (draggedWorkspace && draggedWorkspace !== workspaceId) {\n            // Determine if we should insert above or below based on mouse position\n            const rect = e.currentTarget.getBoundingClientRect();\n            const midY = rect.top + rect.height / 2;\n            const insertAbove = e.clientY < midY;\n            setDragOverWorkspace(workspaceId);\n            // Store the insert position for visual feedback\n            e.currentTarget.dataset.insertAbove = insertAbove.toString();\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOverWorkspace(null);\n        // Clear the insert position\n        e.currentTarget.dataset.insertAbove = '';\n    };\n    const handleDragEnd = ()=>{\n        setDraggedWorkspace(null);\n        setDragOverWorkspace(null);\n        setDraggedConversation(null);\n        setDragOverTarget(null);\n    };\n    // Conversation drag and drop handlers\n    const handleConversationDragStart = (e, conversationId)=>{\n        setDraggedConversation(conversationId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', conversationId);\n    };\n    const handleWorkspaceDropZone = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (draggedConversation) {\n            setDragOverTarget({\n                type: workspaceId ? 'workspace' : 'unassigned',\n                id: workspaceId\n            });\n        }\n    };\n    const handleConversationDrop = async (e, targetWorkspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (!draggedConversation) return;\n        try {\n            await onAssignConversationToWorkspace(draggedConversation, targetWorkspaceId || null);\n        } catch (error) {\n            console.error('Failed to move conversation:', error);\n        } finally{\n            setDraggedConversation(null);\n            setDragOverTarget(null);\n        }\n    };\n    const handleDropBetween = async (insertIndex)=>{\n        if (!draggedWorkspace) {\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n            return;\n        }\n        try {\n            // Find the index of the dragged workspace\n            const draggedIndex = workspaces.findIndex((w)=>w.id === draggedWorkspace);\n            if (draggedIndex === -1) return;\n            console.log('Sidebar: handleDropBetween called', {\n                draggedWorkspace,\n                draggedIndex,\n                insertIndex,\n                totalWorkspaces: workspaces.length,\n                workspaceIds: workspaces.map((w)=>w.id)\n            });\n            // Adjust insert index if dragging down\n            let adjustedIndex = insertIndex;\n            if (draggedIndex < insertIndex) {\n                adjustedIndex = insertIndex - 1;\n            }\n            console.log('Sidebar: Adjusted index:', adjustedIndex);\n            // Check if the position is actually changing\n            if (draggedIndex === adjustedIndex) {\n                console.log('Sidebar: No position change needed, skipping reorder');\n                return;\n            }\n            // Create new order\n            const newWorkspaces = [\n                ...workspaces\n            ];\n            const [draggedItem] = newWorkspaces.splice(draggedIndex, 1);\n            newWorkspaces.splice(adjustedIndex, 0, draggedItem);\n            // Update positions and call reorder API\n            const workspaceIds = newWorkspaces.map((w)=>w.id);\n            console.log('Sidebar: Calling reorder with new order:', workspaceIds);\n            await onReorderWorkspaces(workspaceIds);\n        } catch (error) {\n            console.error('Failed to reorder workspaces:', error);\n        } finally{\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n        }\n    };\n    // Group conversations by workspace\n    const unassignedConversations = conversations.filter((conv)=>!conv.workspaceId);\n    const workspaceConversations = workspaces.reduce((acc, workspace)=>{\n        acc[workspace.id] = conversations.filter((conv)=>conv.workspaceId === workspace.id);\n        return acc;\n    }, {});\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) {\n            return 'Today';\n        } else if (diffInDays === 1) {\n            return 'Yesterday';\n        } else if (diffInDays < 7) {\n            return \"\".concat(diffInDays, \" days ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out\", \"w-96 md:w-80\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"md:relative md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Nexus\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"p-2 hover:bg-sidebar-accent rounded-lg md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewChat,\n                                className: \"w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Chat\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-sidebar-muted\",\n                                                    children: \"Workspaces\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowWorkspaceForm(true),\n                                                    className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                    title: \"Create workspace\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        showWorkspaceForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCreateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Create\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setShowWorkspaceForm(false);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        editingWorkspaceId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpdateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Update\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setEditingWorkspaceId(null);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                workspaces.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-sidebar-muted mb-3 px-1\",\n                                            children: \"My Workspaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === 'above-first-workspace' && \"bg-blue-500 rounded\"),\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        setDragOverWorkspace('above-first-workspace');\n                                                    },\n                                                    onDragLeave: ()=>setDragOverWorkspace(null),\n                                                    onDrop: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleDropBetween(0); // Insert at the beginning\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this),\n                                                workspaces.map((workspace, index)=>{\n                                                    var _workspaceConversations_workspace_id;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            draggedWorkspace && draggedWorkspace !== workspace.id && index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === \"above-\".concat(workspace.id) && \"bg-blue-500 rounded\"),\n                                                                onDragOver: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.stopPropagation();\n                                                                    setDragOverWorkspace(\"above-\".concat(workspace.id));\n                                                                },\n                                                                onDragLeave: ()=>setDragOverWorkspace(null),\n                                                                onDrop: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.stopPropagation();\n                                                                    handleDropBetween(index);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex items-center gap-2 p-2 rounded-lg transition-all duration-200 relative cursor-move\", draggedWorkspace === workspace.id && \"opacity-50 scale-95\", draggedWorkspace && draggedWorkspace !== workspace.id && \"opacity-75\"),\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleDragStart(e, workspace.id),\n                                                                onDragEnd: handleDragEnd,\n                                                                onMouseEnter: ()=>setHoveredWorkspace(workspace.id),\n                                                                onMouseLeave: ()=>setHoveredWorkspace(null),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: workspace.color\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-sidebar-foreground truncate\",\n                                                                        children: workspace.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-sidebar-muted\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            ((_workspaceConversations_workspace_id = workspaceConversations[workspace.id]) === null || _workspaceConversations_workspace_id === void 0 ? void 0 : _workspaceConversations_workspace_id.length) || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-1 ml-auto\",\n                                                                        draggable: false,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleCreateConversationInWorkspace(workspace.id),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-green-500/20 rounded-md text-green-400 hover:text-green-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"New conversation in workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditWorkspace(workspace),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-sidebar-accent rounded-md transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"Edit workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 569,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 559,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleDeleteWorkspace(workspace.id),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"Delete workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, workspace.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 transition-all duration-200\", dragOverWorkspace === 'below-all-workspaces' && \"bg-blue-500 rounded\"),\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        setDragOverWorkspace('below-all-workspaces');\n                                                    },\n                                                    onDragLeave: ()=>setDragOverWorkspace(null),\n                                                    onDrop: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleDropBetween(workspaces.length);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sidebar-muted text-center py-8\",\n                                    children: \"No conversations yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-sidebar-muted mb-3 px-1\",\n                                            children: \"Conversations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this),\n                                        unassignedConversations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors\", (dragOverTarget === null || dragOverTarget === void 0 ? void 0 : dragOverTarget.type) === 'unassigned' && \"bg-green-500/20 border-2 border-green-500 border-dashed\"),\n                                                    onDragOver: (e)=>draggedConversation && handleWorkspaceDropZone(e),\n                                                    onDrop: (e)=>draggedConversation && handleConversationDrop(e),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 text-sidebar-muted\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-sidebar-muted\",\n                                                            children: \"Unassigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: [\n                                                                \"(\",\n                                                                unassignedConversations.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 ml-6\",\n                                                    children: unassignedConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative p-2 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\", draggedConversation === conversation.id && \"opacity-50\"),\n                                                            draggable: true,\n                                                            onDragStart: (e)=>handleConversationDragStart(e, conversation.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            onClick: ()=>onSelectConversation(conversation.id),\n                                                            onMouseEnter: ()=>setHoveredId(conversation.id),\n                                                            onMouseLeave: ()=>setHoveredId(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: editTitle,\n                                                                            onChange: (e)=>setEditTitle(e.target.value),\n                                                                            onBlur: handleSaveEdit,\n                                                                            onKeyDown: (e)=>{\n                                                                                if (e.key === 'Enter') handleSaveEdit();\n                                                                                if (e.key === 'Escape') handleCancelEdit();\n                                                                            },\n                                                                            className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                                            autoFocus: true,\n                                                                            onClick: (e)=>e.stopPropagation()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: conversation.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-sidebar-muted mt-1\",\n                                                                                    children: formatDate(conversation.updatedAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 655,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex gap-1 transition-opacity duration-200\", hoveredId === conversation.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleStartEdit(conversation);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                title: \"Edit conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 695,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            setShowConversationMenu(showConversationMenu === conversation.id ? null : conversation.id);\n                                                                                        },\n                                                                                        className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                        title: \"Move to workspace\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"w-3 h-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 708,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 698,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    showConversationMenu === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: (e)=>{\n                                                                                                        e.stopPropagation();\n                                                                                                        handleAssignConversation(conversation.id, null);\n                                                                                                    },\n                                                                                                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors\",\n                                                                                                    children: \"\\uD83D\\uDCDD Unassigned\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                    lineNumber: 715,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                workspaces.map((ws)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                            handleAssignConversation(conversation.id, ws.id);\n                                                                                                        },\n                                                                                                        className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"w-2 h-2 rounded-full flex-shrink-0\",\n                                                                                                                style: {\n                                                                                                                    backgroundColor: ws.color\n                                                                                                                }\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                                lineNumber: 733,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this),\n                                                                                                            ws.name\n                                                                                                        ]\n                                                                                                    }, ws.id, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                        lineNumber: 725,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 714,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 713,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    onDeleteConversation(conversation.id);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors\",\n                                                                                title: \"Delete conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, conversation.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 p-3 border-t border-sidebar-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/files'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"File Manager\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this),\n                                            settings.zepSettings.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/memory'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Memory Management\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onOpenSettings,\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Settings\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SyncStatus__WEBPACK_IMPORTED_MODULE_4__.SyncStatus, {\n                                        className: \"text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 770,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 769,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 809,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 805,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"fS+x1BkQ+GCvqupkBAwbiWVf/5M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});