"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/toolTagParser.ts":
/*!************************************!*\
  !*** ./src/utils/toolTagParser.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatToolCall: () => (/* binding */ formatToolCall),\n/* harmony export */   parseToolTag: () => (/* binding */ parseToolTag),\n/* harmony export */   processToolTags: () => (/* binding */ processToolTags),\n/* harmony export */   processToolTagsWithCollapse: () => (/* binding */ processToolTagsWithCollapse)\n/* harmony export */ });\n/**\n * Utility functions for parsing and formatting <tool> tags in AI responses\n */ /**\n * Parses a <tool> tag content and extracts the JSON data\n */ function parseToolTag(toolContent) {\n    try {\n        // Remove any extra whitespace and newlines\n        const cleanContent = toolContent.trim();\n        // Try to parse as JSON\n        const parsed = JSON.parse(cleanContent);\n        // Validate that it has the expected structure\n        if (typeof parsed === 'object' && parsed !== null) {\n            return {\n                name: parsed.name || 'unknown',\n                arguments: parsed.arguments || {},\n                summary: parsed.summary,\n                final_message: parsed.final_message\n            };\n        }\n        return null;\n    } catch (error) {\n        console.warn('Failed to parse tool tag content:', error);\n        return null;\n    }\n}\n/**\n * Formats a tool call into a readable markdown representation\n */ function formatToolCall(toolCall) {\n    const { name, arguments: args, summary, final_message } = toolCall;\n    let formatted = \"### \\uD83D\\uDD27 Tool Call: \".concat(name, \"\\n\\n\");\n    // Add summary if available\n    if (summary) {\n        formatted += \"**Summary:** \".concat(summary, \"\\n\\n\");\n    }\n    // Add arguments if they exist and are not empty\n    if (args && Object.keys(args).length > 0) {\n        formatted += \"**Arguments:**\\n\";\n        formatted += '```json\\n';\n        formatted += JSON.stringify(args, null, 2);\n        formatted += '\\n```\\n\\n';\n    }\n    // Add final message if available\n    if (final_message) {\n        formatted += \"**Result:** \".concat(final_message, \"\\n\\n\");\n    }\n    return formatted;\n}\n/**\n * Processes message content to replace <tool> tags with formatted content\n */ function processToolTags(content) {\n    // Regular expression to match <tool>...</tool> tags (including variations like <tool call>)\n    const toolTagRegex = /<tool[^>]*>([\\s\\S]*?)<\\/tool[^>]*>/gi;\n    return content.replace(toolTagRegex, (match, toolContent)=>{\n        const toolCall = parseToolTag(toolContent);\n        if (toolCall) {\n            return formatToolCall(toolCall);\n        } else {\n            // If parsing fails, wrap in a code block to make it more readable\n            return \"\\n```\\n\".concat(toolContent.trim(), \"\\n```\\n\");\n        }\n    });\n}\n/**\n * Alternative processing that creates collapsible sections for tool calls\n */ function processToolTagsWithCollapse(content) {\n    const toolTagRegex = /<tool[^>]*>([\\s\\S]*?)<\\/tool>/gi;\n    return content.replace(toolTagRegex, (match, toolContent)=>{\n        const toolCall = parseToolTag(toolContent);\n        if (toolCall) {\n            const { name, arguments: args, summary, final_message } = toolCall;\n            let formatted = \"<details>\\n<summary><strong>\\uD83D\\uDD27 Tool Call: \".concat(name, \"</strong>\");\n            if (summary) {\n                formatted += \" - \".concat(summary);\n            }\n            formatted += \"</summary>\\n\\n\";\n            if (args && Object.keys(args).length > 0) {\n                formatted += \"**Arguments:**\\n```json\\n\".concat(JSON.stringify(args, null, 2), \"\\n```\\n\\n\");\n            }\n            if (final_message) {\n                formatted += \"**Result:** \".concat(final_message, \"\\n\\n\");\n            }\n            formatted += \"</details>\\n\\n\";\n            return formatted;\n        } else {\n            // If parsing fails, wrap in a collapsible code block\n            return \"<details>\\n<summary><strong>\\uD83D\\uDD27 Tool Output</strong></summary>\\n\\n```\\n\".concat(toolContent.trim(), \"\\n```\\n\\n</details>\\n\\n\");\n        }\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/toolTagParser.ts\n"));

/***/ })

});