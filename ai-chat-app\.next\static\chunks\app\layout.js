/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/katex/dist/katex.min.css":
/*!***********************************************!*\
  !*** ./node_modules/katex/dist/katex.min.css ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d0c0338de43d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9rYXRleC9kaXN0L2thdGV4Lm1pbi5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERldlxcQUlcXENoYXRcXGNoYXQxMVxcYWktY2hhdC1hcHBcXG5vZGVfbW9kdWxlc1xca2F0ZXhcXGRpc3RcXGthdGV4Lm1pbi5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkMGMwMzM4ZGU0M2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/katex/dist/katex.min.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={432:(e,r,t)=>{var n=t(887);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},887:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(432);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxnQkFBZ0I7QUFDcEIsSUFBSSxjQUFjO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcY2pzXFxyZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBSZWFjdFxuICogcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzXG4gKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5cInVzZSBzdHJpY3RcIjtcblwicHJvZHVjdGlvblwiICE9PSBwcm9jZXNzLmVudi5OT0RFX0VOViAmJlxuICAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKSB7XG4gICAgICBpZiAobnVsbCA9PSB0eXBlKSByZXR1cm4gbnVsbDtcbiAgICAgIGlmIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICByZXR1cm4gdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRVxuICAgICAgICAgID8gbnVsbFxuICAgICAgICAgIDogdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgbnVsbDtcbiAgICAgIGlmIChcInN0cmluZ1wiID09PSB0eXBlb2YgdHlwZSkgcmV0dXJuIHR5cGU7XG4gICAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgICAgY2FzZSBSRUFDVF9GUkFHTUVOVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkZyYWdtZW50XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfUFJPRklMRVJfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJQcm9maWxlclwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NUUklDVF9NT0RFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3RyaWN0TW9kZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VMaXN0XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfQUNUSVZJVFlfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJBY3Rpdml0eVwiO1xuICAgICAgfVxuICAgICAgaWYgKFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICBzd2l0Y2ggKFxuICAgICAgICAgIChcIm51bWJlclwiID09PSB0eXBlb2YgdHlwZS50YWcgJiZcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVjZWl2ZWQgYW4gdW5leHBlY3RlZCBvYmplY3QgaW4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKCkuIFRoaXMgaXMgbGlrZWx5IGEgYnVnIGluIFJlYWN0LiBQbGVhc2UgZmlsZSBhbiBpc3N1ZS5cIlxuICAgICAgICAgICAgKSxcbiAgICAgICAgICB0eXBlLiQkdHlwZW9mKVxuICAgICAgICApIHtcbiAgICAgICAgICBjYXNlIFJFQUNUX1BPUlRBTF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIFwiUG9ydGFsXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05URVhUX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gdHlwZS5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlNVTUVSX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuX2NvbnRleHQuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuQ29uc3VtZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEU6XG4gICAgICAgICAgICB2YXIgaW5uZXJUeXBlID0gdHlwZS5yZW5kZXI7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5kaXNwbGF5TmFtZTtcbiAgICAgICAgICAgIHR5cGUgfHxcbiAgICAgICAgICAgICAgKCh0eXBlID0gaW5uZXJUeXBlLmRpc3BsYXlOYW1lIHx8IGlubmVyVHlwZS5uYW1lIHx8IFwiXCIpLFxuICAgICAgICAgICAgICAodHlwZSA9IFwiXCIgIT09IHR5cGUgPyBcIkZvcndhcmRSZWYoXCIgKyB0eXBlICsgXCIpXCIgOiBcIkZvcndhcmRSZWZcIikpO1xuICAgICAgICAgICAgcmV0dXJuIHR5cGU7XG4gICAgICAgICAgY2FzZSBSRUFDVF9NRU1PX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAoaW5uZXJUeXBlID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsKSxcbiAgICAgICAgICAgICAgbnVsbCAhPT0gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgPyBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA6IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8IFwiTWVtb1wiXG4gICAgICAgICAgICApO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTEFaWV9UWVBFOlxuICAgICAgICAgICAgaW5uZXJUeXBlID0gdHlwZS5fcGF5bG9hZDtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLl9pbml0O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKGlubmVyVHlwZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoeCkge31cbiAgICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIFwiXCIgKyB2YWx1ZTtcbiAgICB9XG4gICAgZnVuY3Rpb24gY2hlY2tLZXlTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICExO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMDtcbiAgICAgIH1cbiAgICAgIGlmIChKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gY29uc29sZTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfdGVtcF9jb25zdCA9IEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdC5lcnJvcjtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMCA9XG4gICAgICAgICAgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIFN5bWJvbCAmJlxuICAgICAgICAgICAgU3ltYm9sLnRvU3RyaW5nVGFnICYmXG4gICAgICAgICAgICB2YWx1ZVtTeW1ib2wudG9TdHJpbmdUYWddKSB8fFxuICAgICAgICAgIHZhbHVlLmNvbnN0cnVjdG9yLm5hbWUgfHxcbiAgICAgICAgICBcIk9iamVjdFwiO1xuICAgICAgICBKU0NvbXBpbGVyX3RlbXBfY29uc3QuY2FsbChcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQsXG4gICAgICAgICAgXCJUaGUgcHJvdmlkZWQga2V5IGlzIGFuIHVuc3VwcG9ydGVkIHR5cGUgJXMuIFRoaXMgdmFsdWUgbXVzdCBiZSBjb2VyY2VkIHRvIGEgc3RyaW5nIGJlZm9yZSB1c2luZyBpdCBoZXJlLlwiLFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMFxuICAgICAgICApO1xuICAgICAgICByZXR1cm4gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0VGFza05hbWUodHlwZSkge1xuICAgICAgaWYgKHR5cGUgPT09IFJFQUNUX0ZSQUdNRU5UX1RZUEUpIHJldHVybiBcIjw+XCI7XG4gICAgICBpZiAoXG4gICAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlICYmXG4gICAgICAgIG51bGwgIT09IHR5cGUgJiZcbiAgICAgICAgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfTEFaWV9UWVBFXG4gICAgICApXG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB0cnkge1xuICAgICAgICB2YXIgbmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgcmV0dXJuIG5hbWUgPyBcIjxcIiArIG5hbWUgKyBcIj5cIiA6IFwiPC4uLj5cIjtcbiAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0T3duZXIoKSB7XG4gICAgICB2YXIgZGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLkE7XG4gICAgICByZXR1cm4gbnVsbCA9PT0gZGlzcGF0Y2hlciA/IG51bGwgOiBkaXNwYXRjaGVyLmdldE93bmVyKCk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFVua25vd25Pd25lcigpIHtcbiAgICAgIHJldHVybiBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gaGFzVmFsaWRLZXkoY29uZmlnKSB7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIHZhciBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGNvbmZpZywgXCJrZXlcIikuZ2V0O1xuICAgICAgICBpZiAoZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZykgcmV0dXJuICExO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29uZmlnLmtleTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKSB7XG4gICAgICBmdW5jdGlvbiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkoKSB7XG4gICAgICAgIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duIHx8XG4gICAgICAgICAgKChzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biA9ICEwKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgXCIlczogYGtleWAgaXMgbm90IGEgcHJvcC4gVHJ5aW5nIHRvIGFjY2VzcyBpdCB3aWxsIHJlc3VsdCBpbiBgdW5kZWZpbmVkYCBiZWluZyByZXR1cm5lZC4gSWYgeW91IG5lZWQgdG8gYWNjZXNzIHRoZSBzYW1lIHZhbHVlIHdpdGhpbiB0aGUgY2hpbGQgY29tcG9uZW50LCB5b3Ugc2hvdWxkIHBhc3MgaXQgYXMgYSBkaWZmZXJlbnQgcHJvcC4gKGh0dHBzOi8vcmVhY3QuZGV2L2xpbmsvc3BlY2lhbC1wcm9wcylcIixcbiAgICAgICAgICAgIGRpc3BsYXlOYW1lXG4gICAgICAgICAgKSk7XG4gICAgICB9XG4gICAgICB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkuaXNSZWFjdFdhcm5pbmcgPSAhMDtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShwcm9wcywgXCJrZXlcIiwge1xuICAgICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ0tleSxcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMFxuICAgICAgfSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nKCkge1xuICAgICAgdmFyIGNvbXBvbmVudE5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodGhpcy50eXBlKTtcbiAgICAgIGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gfHxcbiAgICAgICAgKChkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdID0gITApLFxuICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgIFwiQWNjZXNzaW5nIGVsZW1lbnQucmVmIHdhcyByZW1vdmVkIGluIFJlYWN0IDE5LiByZWYgaXMgbm93IGEgcmVndWxhciBwcm9wLiBJdCB3aWxsIGJlIHJlbW92ZWQgZnJvbSB0aGUgSlNYIEVsZW1lbnQgdHlwZSBpbiBhIGZ1dHVyZSByZWxlYXNlLlwiXG4gICAgICAgICkpO1xuICAgICAgY29tcG9uZW50TmFtZSA9IHRoaXMucHJvcHMucmVmO1xuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29tcG9uZW50TmFtZSA/IGNvbXBvbmVudE5hbWUgOiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiBSZWFjdEVsZW1lbnQoXG4gICAgICB0eXBlLFxuICAgICAga2V5LFxuICAgICAgc2VsZixcbiAgICAgIHNvdXJjZSxcbiAgICAgIG93bmVyLFxuICAgICAgcHJvcHMsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICBzZWxmID0gcHJvcHMucmVmO1xuICAgICAgdHlwZSA9IHtcbiAgICAgICAgJCR0eXBlb2Y6IFJFQUNUX0VMRU1FTlRfVFlQRSxcbiAgICAgICAgdHlwZTogdHlwZSxcbiAgICAgICAga2V5OiBrZXksXG4gICAgICAgIHByb3BzOiBwcm9wcyxcbiAgICAgICAgX293bmVyOiBvd25lclxuICAgICAgfTtcbiAgICAgIG51bGwgIT09ICh2b2lkIDAgIT09IHNlbGYgPyBzZWxmIDogbnVsbClcbiAgICAgICAgPyBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgICAgICBnZXQ6IGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nXG4gICAgICAgICAgfSlcbiAgICAgICAgOiBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwgeyBlbnVtZXJhYmxlOiAhMSwgdmFsdWU6IG51bGwgfSk7XG4gICAgICB0eXBlLl9zdG9yZSA9IHt9O1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUuX3N0b3JlLCBcInZhbGlkYXRlZFwiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiAwXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z0luZm9cIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdTdGFja1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1N0YWNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1Rhc2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdUYXNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5mcmVlemUgJiYgKE9iamVjdC5mcmVlemUodHlwZS5wcm9wcyksIE9iamVjdC5mcmVlemUodHlwZSkpO1xuICAgICAgcmV0dXJuIHR5cGU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGpzeERFVkltcGwoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZixcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHZhciBjaGlsZHJlbiA9IGNvbmZpZy5jaGlsZHJlbjtcbiAgICAgIGlmICh2b2lkIDAgIT09IGNoaWxkcmVuKVxuICAgICAgICBpZiAoaXNTdGF0aWNDaGlsZHJlbilcbiAgICAgICAgICBpZiAoaXNBcnJheUltcGwoY2hpbGRyZW4pKSB7XG4gICAgICAgICAgICBmb3IgKFxuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID0gMDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA8IGNoaWxkcmVuLmxlbmd0aDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbisrXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAgIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuW2lzU3RhdGljQ2hpbGRyZW5dKTtcbiAgICAgICAgICAgIE9iamVjdC5mcmVlemUgJiYgT2JqZWN0LmZyZWV6ZShjaGlsZHJlbik7XG4gICAgICAgICAgfSBlbHNlXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlYWN0LmpzeDogU3RhdGljIGNoaWxkcmVuIHNob3VsZCBhbHdheXMgYmUgYW4gYXJyYXkuIFlvdSBhcmUgbGlrZWx5IGV4cGxpY2l0bHkgY2FsbGluZyBSZWFjdC5qc3hzIG9yIFJlYWN0LmpzeERFVi4gVXNlIHRoZSBCYWJlbCB0cmFuc2Zvcm0gaW5zdGVhZC5cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgZWxzZSB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbik7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIGNoaWxkcmVuID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICB2YXIga2V5cyA9IE9iamVjdC5rZXlzKGNvbmZpZykuZmlsdGVyKGZ1bmN0aW9uIChrKSB7XG4gICAgICAgICAgcmV0dXJuIFwia2V5XCIgIT09IGs7XG4gICAgICAgIH0pO1xuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID1cbiAgICAgICAgICAwIDwga2V5cy5sZW5ndGhcbiAgICAgICAgICAgID8gXCJ7a2V5OiBzb21lS2V5LCBcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiXG4gICAgICAgICAgICA6IFwie2tleTogc29tZUtleX1cIjtcbiAgICAgICAgZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gfHxcbiAgICAgICAgICAoKGtleXMgPVxuICAgICAgICAgICAgMCA8IGtleXMubGVuZ3RoID8gXCJ7XCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIiA6IFwie31cIiksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICdBIHByb3BzIG9iamVjdCBjb250YWluaW5nIGEgXCJrZXlcIiBwcm9wIGlzIGJlaW5nIHNwcmVhZCBpbnRvIEpTWDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIHsuLi5wcm9wc30gLz5cXG5SZWFjdCBrZXlzIG11c3QgYmUgcGFzc2VkIGRpcmVjdGx5IHRvIEpTWCB3aXRob3V0IHVzaW5nIHNwcmVhZDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIGtleT17c29tZUtleX0gey4uLnByb3BzfSAvPicsXG4gICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgICAgICBrZXlzLFxuICAgICAgICAgICAgY2hpbGRyZW5cbiAgICAgICAgICApLFxuICAgICAgICAgIChkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSA9ICEwKSk7XG4gICAgICB9XG4gICAgICBjaGlsZHJlbiA9IG51bGw7XG4gICAgICB2b2lkIDAgIT09IG1heWJlS2V5ICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKG1heWJlS2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIG1heWJlS2V5KSk7XG4gICAgICBoYXNWYWxpZEtleShjb25maWcpICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKGNvbmZpZy5rZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgY29uZmlnLmtleSkpO1xuICAgICAgaWYgKFwia2V5XCIgaW4gY29uZmlnKSB7XG4gICAgICAgIG1heWJlS2V5ID0ge307XG4gICAgICAgIGZvciAodmFyIHByb3BOYW1lIGluIGNvbmZpZylcbiAgICAgICAgICBcImtleVwiICE9PSBwcm9wTmFtZSAmJiAobWF5YmVLZXlbcHJvcE5hbWVdID0gY29uZmlnW3Byb3BOYW1lXSk7XG4gICAgICB9IGVsc2UgbWF5YmVLZXkgPSBjb25maWc7XG4gICAgICBjaGlsZHJlbiAmJlxuICAgICAgICBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihcbiAgICAgICAgICBtYXliZUtleSxcbiAgICAgICAgICBcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlXG4gICAgICAgICAgICA/IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IFwiVW5rbm93blwiXG4gICAgICAgICAgICA6IHR5cGVcbiAgICAgICAgKTtcbiAgICAgIHJldHVybiBSZWFjdEVsZW1lbnQoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNoaWxkcmVuLFxuICAgICAgICBzZWxmLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIGdldE93bmVyKCksXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgICBkZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHZhbGlkYXRlQ2hpbGRLZXlzKG5vZGUpIHtcbiAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiBub2RlICYmXG4gICAgICAgIG51bGwgIT09IG5vZGUgJiZcbiAgICAgICAgbm9kZS4kJHR5cGVvZiA9PT0gUkVBQ1RfRUxFTUVOVF9UWVBFICYmXG4gICAgICAgIG5vZGUuX3N0b3JlICYmXG4gICAgICAgIChub2RlLl9zdG9yZS52YWxpZGF0ZWQgPSAxKTtcbiAgICB9XG4gICAgdmFyIFJlYWN0ID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9yZWFjdFwiKSxcbiAgICAgIFJFQUNUX0VMRU1FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC50cmFuc2l0aW9uYWwuZWxlbWVudFwiKSxcbiAgICAgIFJFQUNUX1BPUlRBTF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnBvcnRhbFwiKSxcbiAgICAgIFJFQUNUX0ZSQUdNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZnJhZ21lbnRcIiksXG4gICAgICBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN0cmljdF9tb2RlXCIpLFxuICAgICAgUkVBQ1RfUFJPRklMRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wcm9maWxlclwiKSxcbiAgICAgIFJFQUNUX0NPTlNVTUVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29uc3VtZXJcIiksXG4gICAgICBSRUFDVF9DT05URVhUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29udGV4dFwiKSxcbiAgICAgIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZm9yd2FyZF9yZWZcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlX2xpc3RcIiksXG4gICAgICBSRUFDVF9NRU1PX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubWVtb1wiKSxcbiAgICAgIFJFQUNUX0xBWllfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5sYXp5XCIpLFxuICAgICAgUkVBQ1RfQUNUSVZJVFlfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5hY3Rpdml0eVwiKSxcbiAgICAgIFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UgPSBTeW1ib2wuZm9yKFwicmVhY3QuY2xpZW50LnJlZmVyZW5jZVwiKSxcbiAgICAgIFJlYWN0U2hhcmVkSW50ZXJuYWxzID1cbiAgICAgICAgUmVhY3QuX19DTElFTlRfSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFLFxuICAgICAgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LFxuICAgICAgaXNBcnJheUltcGwgPSBBcnJheS5pc0FycmF5LFxuICAgICAgY3JlYXRlVGFzayA9IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA/IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH07XG4gICAgUmVhY3QgPSB7XG4gICAgICByZWFjdF9zdGFja19ib3R0b21fZnJhbWU6IGZ1bmN0aW9uIChjYWxsU3RhY2tGb3JFcnJvcikge1xuICAgICAgICByZXR1cm4gY2FsbFN0YWNrRm9yRXJyb3IoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHZhciBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1N0YWNrID0gUmVhY3QucmVhY3Rfc3RhY2tfYm90dG9tX2ZyYW1lLmJpbmQoXG4gICAgICBSZWFjdCxcbiAgICAgIFVua25vd25Pd25lclxuICAgICkoKTtcbiAgICB2YXIgdW5rbm93bk93bmVyRGVidWdUYXNrID0gY3JlYXRlVGFzayhnZXRUYXNrTmFtZShVbmtub3duT3duZXIpKTtcbiAgICB2YXIgZGlkV2FybkFib3V0S2V5U3ByZWFkID0ge307XG4gICAgZXhwb3J0cy5GcmFnbWVudCA9IFJFQUNUX0ZSQUdNRU5UX1RZUEU7XG4gICAgZXhwb3J0cy5qc3hERVYgPSBmdW5jdGlvbiAoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZlxuICAgICkge1xuICAgICAgdmFyIHRyYWNrQWN0dWFsT3duZXIgPVxuICAgICAgICAxZTQgPiBSZWFjdFNoYXJlZEludGVybmFscy5yZWNlbnRseUNyZWF0ZWRPd25lclN0YWNrcysrO1xuICAgICAgcmV0dXJuIGpzeERFVkltcGwoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNvbmZpZyxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgc2VsZixcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lclxuICAgICAgICAgID8gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIilcbiAgICAgICAgICA6IHVua25vd25Pd25lckRlYnVnU3RhY2ssXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXIgPyBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKHR5cGUpKSA6IHVua25vd25Pd25lckRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9O1xuICB9KSgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist', 'Geist Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_5cfdac\",\"variable\":\"__variable_5cfdac\"};\n    if(true) {\n      // 1753934267108\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0XCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1nZWlzdC1zYW5zXCIsXCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiZ2Vpc3RTYW5zXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsOERBQThEO0FBQ3pGLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFrSSxjQUFjLHNEQUFzRDtBQUNwTyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERldlxcQUlcXENoYXRcXGNoYXQxMVxcYWktY2hhdC1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZm9udFxcZ29vZ2xlXFx0YXJnZXQuY3NzP3tcInBhdGhcIjpcInNyY1xcYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0XCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1nZWlzdC1zYW5zXCIsXCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiZ2Vpc3RTYW5zXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidHZWlzdCcsICdHZWlzdCBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV81Y2ZkYWNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlXzVjZmRhY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUzOTM0MjY3MTA4XG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL1VTRVIvRGV2L0FJL0NoYXQvY2hhdDExL2FpLWNoYXQtYXBwL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist Mono', 'Geist Mono Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_9a8899\",\"variable\":\"__variable_9a8899\"};\n    if(true) {\n      // 1753934267110\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0X01vbm9cIixcImFyZ3VtZW50c1wiOlt7XCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LW1vbm9cIixcInN1YnNldHNcIjpbXCJsYXRpblwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJnZWlzdE1vbm9cIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyx3RUFBd0U7QUFDbkcsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQWtJLGNBQWMsc0RBQXNEO0FBQ3BPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxmb250XFxnb29nbGVcXHRhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxhcHBcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiR2Vpc3RfTW9ub1wiLFwiYXJndW1lbnRzXCI6W3tcInZhcmlhYmxlXCI6XCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImdlaXN0TW9ub1wifXxhcHAtcGFnZXMtYnJvd3NlciJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInR2Vpc3QgTW9ubycsICdHZWlzdCBNb25vIEZhbGxiYWNrJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lXzlhODg5OVwiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfOWE4ODk5XCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTM5MzQyNjcxMTBcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiQzovVXNlcnMvVVNFUi9EZXYvQUkvQ2hhdC9jaGF0MTEvYWktY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"83858a56bea1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERldlxcQUlcXENoYXRcXGNoYXQxMVxcYWktY2hhdC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjgzODU4YTU2YmVhMVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/settings-service */ \"(app-pages-browser)/./src/lib/settings-service.ts\");\n/* __next_internal_client_entry_do_not_use__ SettingsProvider,useSettings,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Get default settings with environment variable fallbacks\nfunction getDefaultSettings() {\n    return {\n        theme: 'auto',\n        defaultMode: 'simple',\n        autoSave: true,\n        preferredModels: {\n            simple: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            enhanced: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            orchestrator: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            refine: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            memorySummarization: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            titleGeneration: 'Qwen/Qwen3-235B-A22B-Instruct-2507'\n        },\n        providerSettings: {\n            baseUrl: \"https://llm.chutes.ai/v1\" || 0,\n            apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0,\n            availableModels: []\n        },\n        agentSettings: {\n            maxIterations: 5,\n            temperature: 0.7,\n            enableTools: [\n                'web_search',\n                'calculator',\n                'file_operations'\n            ]\n        },\n        orchestratorSettings: {\n            parallelAgents: 3,\n            taskTimeout: 300,\n            aggregationStrategy: 'consensus',\n            enableCollaboration: true\n        },\n        zepSettings: {\n            enabled: true,\n            apiUrl: \"http://localhost:8001\" || 0,\n            apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0,\n            autoMemoryExtraction: true,\n            memoryRetentionDays: 30,\n            showMemoryInsights: true,\n            minFactRating: 7\n        }\n    };\n}\n// Create context\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Settings provider component\nconst SettingsProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getDefaultSettings());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load settings on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            loadSettings();\n        }\n    }[\"SettingsProvider.useEffect\"], []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            const applyTheme = {\n                \"SettingsProvider.useEffect.applyTheme\": ()=>{\n                    const root = document.documentElement;\n                    // Remove existing theme classes\n                    root.classList.remove('light', 'dark');\n                    if (settings.theme === 'auto') {\n                        // Use system preference\n                        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        root.classList.add(prefersDark ? 'dark' : 'light');\n                    } else {\n                        // Use explicit theme\n                        root.classList.add(settings.theme);\n                    }\n                }\n            }[\"SettingsProvider.useEffect.applyTheme\"];\n            applyTheme();\n            // Listen for system theme changes when in auto mode\n            if (settings.theme === 'auto') {\n                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n                const handleChange = {\n                    \"SettingsProvider.useEffect.handleChange\": ()=>applyTheme()\n                }[\"SettingsProvider.useEffect.handleChange\"];\n                mediaQuery.addEventListener('change', handleChange);\n                return ({\n                    \"SettingsProvider.useEffect\": ()=>{\n                        mediaQuery.removeEventListener('change', handleChange);\n                    }\n                })[\"SettingsProvider.useEffect\"];\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings.theme\n    ]);\n    const loadSettings = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const defaults = getDefaultSettings();\n            // Load from API using SettingsService\n            const apiSettings = await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.getSettings();\n            const mergedSettings = {\n                ...defaults,\n                ...apiSettings,\n                preferredModels: {\n                    ...defaults.preferredModels,\n                    ...apiSettings.preferredModels\n                },\n                providerSettings: {\n                    ...defaults.providerSettings,\n                    ...apiSettings.providerSettings,\n                    // Always override API key with environment variable\n                    apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n                },\n                agentSettings: {\n                    ...defaults.agentSettings,\n                    ...apiSettings.agentSettings\n                },\n                orchestratorSettings: {\n                    ...defaults.orchestratorSettings,\n                    ...apiSettings.orchestratorSettings\n                },\n                zepSettings: {\n                    ...defaults.zepSettings,\n                    ...apiSettings.zepSettings,\n                    // Always override API key with environment variable\n                    apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0\n                }\n            };\n            setSettings(mergedSettings);\n        } catch (err) {\n            console.error('Failed to load settings:', err);\n            setError('Failed to load settings');\n            setSettings(getDefaultSettings());\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateSettings = async (updates)=>{\n        try {\n            setError(null);\n            // Deep merge updates with current settings\n            const newSettings = {\n                ...settings,\n                ...updates,\n                preferredModels: {\n                    ...settings.preferredModels,\n                    ...updates.preferredModels\n                },\n                providerSettings: {\n                    ...settings.providerSettings,\n                    ...updates.providerSettings,\n                    // Always keep API key from environment\n                    apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n                },\n                agentSettings: {\n                    ...settings.agentSettings,\n                    ...updates.agentSettings\n                },\n                orchestratorSettings: {\n                    ...settings.orchestratorSettings,\n                    ...updates.orchestratorSettings\n                },\n                zepSettings: {\n                    ...settings.zepSettings,\n                    ...updates.zepSettings,\n                    // Always keep API key from environment\n                    apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0\n                }\n            };\n            // Filter out sensitive data before saving to database\n            const settingsToSave = {\n                ...newSettings,\n                providerSettings: {\n                    ...newSettings.providerSettings,\n                    apiKey: ''\n                },\n                zepSettings: {\n                    ...newSettings.zepSettings,\n                    apiKey: ''\n                }\n            };\n            // Update local state immediately for responsive UI (with environment variables)\n            setSettings(newSettings);\n            // Save to API (without sensitive data)\n            await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.updateSettings(settingsToSave);\n        } catch (err) {\n            console.error('Failed to update settings:', err);\n            setError('Failed to update settings');\n            throw err;\n        }\n    };\n    const resetSettings = async ()=>{\n        try {\n            setError(null);\n            setSettings(getDefaultSettings());\n            // Reset on API\n            await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.resetSettings();\n        } catch (err) {\n            console.error('Failed to reset settings:', err);\n            setError('Failed to reset settings');\n            throw err;\n        }\n    };\n    const value = {\n        settings,\n        updateSettings,\n        resetSettings,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\contexts\\\\SettingsContext.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsProvider, \"i0S/fsjWuBYU+/TbprYLz+48PIE=\");\n_c = SettingsProvider;\n// Hook to use settings context\nconst useSettings = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\n_s1(useSettings, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsContext);\nvar _c;\n$RefreshReg$(_c, \"SettingsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9TZXR0aW5nc0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV5RjtBQUNoQztBQXFEekQsMkRBQTJEO0FBQzNELFNBQVNNO0lBQ1AsT0FBTztRQUNMQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxpQkFBaUI7WUFDZkMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLGNBQWM7WUFDZEMsUUFBUTtZQUNSQyxxQkFBcUI7WUFDckJDLGlCQUFpQjtRQUNuQjtRQUNBQyxrQkFBa0I7WUFDaEJDLFNBQVNDLDBCQUFzQyxJQUFJLENBQTBCO1lBQzdFRyxRQUFRSCx3R0FBd0MsSUFBSSxDQUFFO1lBQ3RESyxpQkFBaUIsRUFBRTtRQUNyQjtRQUNBQyxlQUFlO1lBQ2JDLGVBQWU7WUFDZkMsYUFBYTtZQUNiQyxhQUFhO2dCQUFDO2dCQUFjO2dCQUFjO2FBQWtCO1FBQzlEO1FBQ0FDLHNCQUFzQjtZQUNwQkMsZ0JBQWdCO1lBQ2hCQyxhQUFhO1lBQ2JDLHFCQUFxQjtZQUNyQkMscUJBQXFCO1FBQ3ZCO1FBQ0FDLGFBQWE7WUFDWEMsU0FBUztZQUNUQyxRQUFRakIsdUJBQW1DLElBQUksQ0FBdUI7WUFDdEVHLFFBQVFILHVKQUFtQyxJQUFJLENBQTJCO1lBQzFFb0Isc0JBQXNCO1lBQ3RCQyxxQkFBcUI7WUFDckJDLG9CQUFvQjtZQUNwQkMsZUFBZTtRQUNqQjtJQUNGO0FBQ0Y7QUFXQSxpQkFBaUI7QUFDakIsTUFBTUMsZ0NBQWtCMUMsb0RBQWFBLENBQWtDMkM7QUFFdkUsOEJBQThCO0FBQ3ZCLE1BQU1DLG1CQUFzRDtRQUFDLEVBQUVDLFFBQVEsRUFBRTs7SUFDOUUsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUc3QywrQ0FBUUEsQ0FBZUc7SUFDdkQsTUFBTSxDQUFDMkMsV0FBV0MsYUFBYSxHQUFHL0MsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0QsT0FBT0MsU0FBUyxHQUFHakQsK0NBQVFBLENBQWdCO0lBRWxELHlCQUF5QjtJQUN6QkMsZ0RBQVNBO3NDQUFDO1lBQ1JpRDtRQUNGO3FDQUFHLEVBQUU7SUFFTCwwQkFBMEI7SUFDMUJqRCxnREFBU0E7c0NBQUM7WUFDUixNQUFNa0Q7eURBQWE7b0JBQ2pCLE1BQU1DLE9BQU9DLFNBQVNDLGVBQWU7b0JBRXJDLGdDQUFnQztvQkFDaENGLEtBQUtHLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDLFNBQVM7b0JBRS9CLElBQUlaLFNBQVN4QyxLQUFLLEtBQUssUUFBUTt3QkFDN0Isd0JBQXdCO3dCQUN4QixNQUFNcUQsY0FBY0MsT0FBT0MsVUFBVSxDQUFDLGdDQUFnQ0MsT0FBTzt3QkFDN0VSLEtBQUtHLFNBQVMsQ0FBQ00sR0FBRyxDQUFDSixjQUFjLFNBQVM7b0JBQzVDLE9BQU87d0JBQ0wscUJBQXFCO3dCQUNyQkwsS0FBS0csU0FBUyxDQUFDTSxHQUFHLENBQUNqQixTQUFTeEMsS0FBSztvQkFDbkM7Z0JBQ0Y7O1lBRUErQztZQUVBLG9EQUFvRDtZQUNwRCxJQUFJUCxTQUFTeEMsS0FBSyxLQUFLLFFBQVE7Z0JBQzdCLE1BQU0wRCxhQUFhSixPQUFPQyxVQUFVLENBQUM7Z0JBQ3JDLE1BQU1JOytEQUFlLElBQU1aOztnQkFDM0JXLFdBQVdFLGdCQUFnQixDQUFDLFVBQVVEO2dCQUV0QztrREFBTzt3QkFDTEQsV0FBV0csbUJBQW1CLENBQUMsVUFBVUY7b0JBQzNDOztZQUNGO1FBQ0Y7cUNBQUc7UUFBQ25CLFNBQVN4QyxLQUFLO0tBQUM7SUFFbkIsTUFBTThDLGVBQWU7UUFDbkIsSUFBSTtZQUNGSCxhQUFhO1lBQ2JFLFNBQVM7WUFFVCxNQUFNaUIsV0FBVy9EO1lBRWpCLHNDQUFzQztZQUN0QyxNQUFNZ0UsY0FBYyxNQUFNakUsa0VBQWVBLENBQUNrRSxXQUFXO1lBQ3JELE1BQU1DLGlCQUFpQjtnQkFDckIsR0FBR0gsUUFBUTtnQkFDWCxHQUFHQyxXQUFXO2dCQUNkNUQsaUJBQWlCO29CQUNmLEdBQUcyRCxTQUFTM0QsZUFBZTtvQkFDM0IsR0FBRzRELFlBQVk1RCxlQUFlO2dCQUNoQztnQkFDQU8sa0JBQWtCO29CQUNoQixHQUFHb0QsU0FBU3BELGdCQUFnQjtvQkFDNUIsR0FBR3FELFlBQVlyRCxnQkFBZ0I7b0JBQy9CLG9EQUFvRDtvQkFDcERLLFFBQVFILHdHQUF3QyxJQUFJLENBQUU7Z0JBQ3hEO2dCQUNBTSxlQUFlO29CQUNiLEdBQUc0QyxTQUFTNUMsYUFBYTtvQkFDekIsR0FBRzZDLFlBQVk3QyxhQUFhO2dCQUM5QjtnQkFDQUksc0JBQXNCO29CQUNwQixHQUFHd0MsU0FBU3hDLG9CQUFvQjtvQkFDaEMsR0FBR3lDLFlBQVl6QyxvQkFBb0I7Z0JBQ3JDO2dCQUNBSyxhQUFhO29CQUNYLEdBQUdtQyxTQUFTbkMsV0FBVztvQkFDdkIsR0FBR29DLFlBQVlwQyxXQUFXO29CQUMxQixvREFBb0Q7b0JBQ3BEWixRQUFRSCx1SkFBbUMsSUFBSSxDQUEyQjtnQkFDNUU7WUFDRjtZQUNBNkIsWUFBWXdCO1FBQ2QsRUFBRSxPQUFPQyxLQUFLO1lBQ1pDLFFBQVF2QixLQUFLLENBQUMsNEJBQTRCc0I7WUFDMUNyQixTQUFTO1lBQ1RKLFlBQVkxQztRQUNkLFNBQVU7WUFDUjRDLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXlCLGlCQUFpQixPQUFPQztRQUM1QixJQUFJO1lBQ0Z4QixTQUFTO1lBRVQsMkNBQTJDO1lBQzNDLE1BQU15QixjQUFjO2dCQUNsQixHQUFHOUIsUUFBUTtnQkFDWCxHQUFHNkIsT0FBTztnQkFDVmxFLGlCQUFpQjtvQkFDZixHQUFHcUMsU0FBU3JDLGVBQWU7b0JBQzNCLEdBQUdrRSxRQUFRbEUsZUFBZTtnQkFDNUI7Z0JBQ0FPLGtCQUFrQjtvQkFDaEIsR0FBRzhCLFNBQVM5QixnQkFBZ0I7b0JBQzVCLEdBQUcyRCxRQUFRM0QsZ0JBQWdCO29CQUMzQix1Q0FBdUM7b0JBQ3ZDSyxRQUFRSCx3R0FBd0MsSUFBSSxDQUFFO2dCQUN4RDtnQkFDQU0sZUFBZTtvQkFDYixHQUFHc0IsU0FBU3RCLGFBQWE7b0JBQ3pCLEdBQUdtRCxRQUFRbkQsYUFBYTtnQkFDMUI7Z0JBQ0FJLHNCQUFzQjtvQkFDcEIsR0FBR2tCLFNBQVNsQixvQkFBb0I7b0JBQ2hDLEdBQUcrQyxRQUFRL0Msb0JBQW9CO2dCQUNqQztnQkFDQUssYUFBYTtvQkFDWCxHQUFHYSxTQUFTYixXQUFXO29CQUN2QixHQUFHMEMsUUFBUTFDLFdBQVc7b0JBQ3RCLHVDQUF1QztvQkFDdkNaLFFBQVFILHVKQUFtQyxJQUFJLENBQTJCO2dCQUM1RTtZQUNGO1lBRUEsc0RBQXNEO1lBQ3RELE1BQU0yRCxpQkFBaUI7Z0JBQ3JCLEdBQUdELFdBQVc7Z0JBQ2Q1RCxrQkFBa0I7b0JBQ2hCLEdBQUc0RCxZQUFZNUQsZ0JBQWdCO29CQUMvQkssUUFBUTtnQkFDVjtnQkFDQVksYUFBYTtvQkFDWCxHQUFHMkMsWUFBWTNDLFdBQVc7b0JBQzFCWixRQUFRO2dCQUNWO1lBQ0Y7WUFFQSxnRkFBZ0Y7WUFDaEYwQixZQUFZNkI7WUFFWix1Q0FBdUM7WUFDdkMsTUFBTXhFLGtFQUFlQSxDQUFDc0UsY0FBYyxDQUFDRztRQUN2QyxFQUFFLE9BQU9MLEtBQUs7WUFDWkMsUUFBUXZCLEtBQUssQ0FBQyw4QkFBOEJzQjtZQUM1Q3JCLFNBQVM7WUFDVCxNQUFNcUI7UUFDUjtJQUNGO0lBRUEsTUFBTU0sZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRjNCLFNBQVM7WUFDVEosWUFBWTFDO1lBRVosZUFBZTtZQUNmLE1BQU1ELGtFQUFlQSxDQUFDMEUsYUFBYTtRQUNyQyxFQUFFLE9BQU9OLEtBQUs7WUFDWkMsUUFBUXZCLEtBQUssQ0FBQyw2QkFBNkJzQjtZQUMzQ3JCLFNBQVM7WUFDVCxNQUFNcUI7UUFDUjtJQUNGO0lBRUEsTUFBTU8sUUFBNkI7UUFDakNqQztRQUNBNEI7UUFDQUk7UUFDQTlCO1FBQ0FFO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1IsZ0JBQWdCc0MsUUFBUTtRQUFDRCxPQUFPQTtrQkFDOUJsQzs7Ozs7O0FBR1AsRUFBRTtHQS9LV0Q7S0FBQUE7QUFpTGIsK0JBQStCO0FBQ3hCLE1BQU1xQyxjQUFjOztJQUN6QixNQUFNQyxVQUFVakYsaURBQVVBLENBQUN5QztJQUMzQixJQUFJd0MsWUFBWXZDLFdBQVc7UUFDekIsTUFBTSxJQUFJd0MsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRTtJQU5XRDtBQVFiLGlFQUFldkMsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxzcmNcXGNvbnRleHRzXFxTZXR0aW5nc0NvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHNldHRpbmdzU2VydmljZSB9IGZyb20gJ0AvbGliL3NldHRpbmdzLXNlcnZpY2UnO1xuXG4vLyBUeXBlcyBmb3Igc2V0dGluZ3NcbmV4cG9ydCBpbnRlcmZhY2UgVXNlclNldHRpbmdzIHtcbiAgLy8gVUkgUHJlZmVyZW5jZXNcbiAgdGhlbWU6ICdsaWdodCcgfCAnZGFyaycgfCAnYXV0byc7XG4gIGRlZmF1bHRNb2RlOiAnc2ltcGxlJyB8ICdlbmhhbmNlZCcgfCAnb3JjaGVzdHJhdG9yJztcbiAgYXV0b1NhdmU6IGJvb2xlYW47XG4gIFxuICAvLyBNb2RlbCBQcmVmZXJlbmNlc1xuICBwcmVmZXJyZWRNb2RlbHM6IHtcbiAgICBzaW1wbGU6IHN0cmluZztcbiAgICBlbmhhbmNlZDogc3RyaW5nO1xuICAgIG9yY2hlc3RyYXRvcjogc3RyaW5nO1xuICAgIHJlZmluZTogc3RyaW5nO1xuICAgIG1lbW9yeVN1bW1hcml6YXRpb246IHN0cmluZztcbiAgICB0aXRsZUdlbmVyYXRpb246IHN0cmluZztcbiAgfTtcbiAgXG4gIC8vIFByb3ZpZGVyIENvbmZpZ3VyYXRpb25cbiAgcHJvdmlkZXJTZXR0aW5nczoge1xuICAgIGJhc2VVcmw6IHN0cmluZztcbiAgICBhcGlLZXk6IHN0cmluZztcbiAgICBhdmFpbGFibGVNb2RlbHM6IHN0cmluZ1tdO1xuICB9O1xuICBcbiAgLy8gQWdlbnQgQ29uZmlndXJhdGlvblxuICBhZ2VudFNldHRpbmdzOiB7XG4gICAgbWF4SXRlcmF0aW9uczogbnVtYmVyO1xuICAgIHRlbXBlcmF0dXJlOiBudW1iZXI7XG4gICAgZW5hYmxlVG9vbHM6IHN0cmluZ1tdO1xuICB9O1xuICBcbiAgLy8gT3JjaGVzdHJhdG9yIENvbmZpZ3VyYXRpb25cbiAgb3JjaGVzdHJhdG9yU2V0dGluZ3M6IHtcbiAgICBwYXJhbGxlbEFnZW50czogbnVtYmVyO1xuICAgIHRhc2tUaW1lb3V0OiBudW1iZXI7XG4gICAgYWdncmVnYXRpb25TdHJhdGVneTogJ2NvbnNlbnN1cycgfCAnbWFqb3JpdHknIHwgJ2Jlc3QnO1xuICAgIGVuYWJsZUNvbGxhYm9yYXRpb246IGJvb2xlYW47XG4gIH07XG4gIFxuICAvLyBaZXAgTWVtb3J5IENvbmZpZ3VyYXRpb25cbiAgemVwU2V0dGluZ3M6IHtcbiAgICBlbmFibGVkOiBib29sZWFuO1xuICAgIGFwaVVybDogc3RyaW5nO1xuICAgIGFwaUtleTogc3RyaW5nO1xuICAgIGF1dG9NZW1vcnlFeHRyYWN0aW9uOiBib29sZWFuO1xuICAgIG1lbW9yeVJldGVudGlvbkRheXM6IG51bWJlcjtcbiAgICBzaG93TWVtb3J5SW5zaWdodHM6IGJvb2xlYW47XG4gICAgbWluRmFjdFJhdGluZzogbnVtYmVyO1xuICB9O1xufVxuXG4vLyBHZXQgZGVmYXVsdCBzZXR0aW5ncyB3aXRoIGVudmlyb25tZW50IHZhcmlhYmxlIGZhbGxiYWNrc1xuZnVuY3Rpb24gZ2V0RGVmYXVsdFNldHRpbmdzKCk6IFVzZXJTZXR0aW5ncyB7XG4gIHJldHVybiB7XG4gICAgdGhlbWU6ICdhdXRvJyxcbiAgICBkZWZhdWx0TW9kZTogJ3NpbXBsZScsXG4gICAgYXV0b1NhdmU6IHRydWUsXG4gICAgcHJlZmVycmVkTW9kZWxzOiB7XG4gICAgICBzaW1wbGU6ICdRd2VuL1F3ZW4zLTIzNUItQTIyQi1JbnN0cnVjdC0yNTA3JyxcbiAgICAgIGVuaGFuY2VkOiAnUXdlbi9Rd2VuMy0yMzVCLUEyMkItSW5zdHJ1Y3QtMjUwNycsXG4gICAgICBvcmNoZXN0cmF0b3I6ICdRd2VuL1F3ZW4zLTIzNUItQTIyQi1JbnN0cnVjdC0yNTA3JyxcbiAgICAgIHJlZmluZTogJ1F3ZW4vUXdlbjMtMjM1Qi1BMjJCLUluc3RydWN0LTI1MDcnLFxuICAgICAgbWVtb3J5U3VtbWFyaXphdGlvbjogJ1F3ZW4vUXdlbjMtMjM1Qi1BMjJCLUluc3RydWN0LTI1MDcnLFxuICAgICAgdGl0bGVHZW5lcmF0aW9uOiAnUXdlbi9Rd2VuMy0yMzVCLUEyMkItSW5zdHJ1Y3QtMjUwNydcbiAgICB9LFxuICAgIHByb3ZpZGVyU2V0dGluZ3M6IHtcbiAgICAgIGJhc2VVcmw6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NIVVRFU19BUElfVVJMIHx8ICdodHRwczovL2xsbS5jaHV0ZXMuYWkvdjEnLFxuICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DSFVURVNfQVBJX1RPS0VOIHx8ICcnLCAvLyBBbHdheXMgZnJvbSBlbnZcbiAgICAgIGF2YWlsYWJsZU1vZGVsczogW11cbiAgICB9LFxuICAgIGFnZW50U2V0dGluZ3M6IHtcbiAgICAgIG1heEl0ZXJhdGlvbnM6IDUsXG4gICAgICB0ZW1wZXJhdHVyZTogMC43LFxuICAgICAgZW5hYmxlVG9vbHM6IFsnd2ViX3NlYXJjaCcsICdjYWxjdWxhdG9yJywgJ2ZpbGVfb3BlcmF0aW9ucyddXG4gICAgfSxcbiAgICBvcmNoZXN0cmF0b3JTZXR0aW5nczoge1xuICAgICAgcGFyYWxsZWxBZ2VudHM6IDMsXG4gICAgICB0YXNrVGltZW91dDogMzAwLFxuICAgICAgYWdncmVnYXRpb25TdHJhdGVneTogJ2NvbnNlbnN1cycsXG4gICAgICBlbmFibGVDb2xsYWJvcmF0aW9uOiB0cnVlXG4gICAgfSxcbiAgICB6ZXBTZXR0aW5nczoge1xuICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgIGFwaVVybDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfWkVQX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMScsXG4gICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1pFUF9BUElfS0VZIHx8ICd5b3VyLWxvY2FsLXplcC1zZWNyZXQta2V5JywgLy8gQWx3YXlzIGZyb20gZW52XG4gICAgICBhdXRvTWVtb3J5RXh0cmFjdGlvbjogdHJ1ZSxcbiAgICAgIG1lbW9yeVJldGVudGlvbkRheXM6IDMwLFxuICAgICAgc2hvd01lbW9yeUluc2lnaHRzOiB0cnVlLFxuICAgICAgbWluRmFjdFJhdGluZzogN1xuICAgIH1cbiAgfTtcbn1cblxuLy8gQ29udGV4dCB0eXBlXG5pbnRlcmZhY2UgU2V0dGluZ3NDb250ZXh0VHlwZSB7XG4gIHNldHRpbmdzOiBVc2VyU2V0dGluZ3M7XG4gIHVwZGF0ZVNldHRpbmdzOiAodXBkYXRlczogUGFydGlhbDxVc2VyU2V0dGluZ3M+KSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZXNldFNldHRpbmdzOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xufVxuXG4vLyBDcmVhdGUgY29udGV4dFxuY29uc3QgU2V0dGluZ3NDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxTZXR0aW5nc0NvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG4vLyBTZXR0aW5ncyBwcm92aWRlciBjb21wb25lbnRcbmV4cG9ydCBjb25zdCBTZXR0aW5nc1Byb3ZpZGVyOiBSZWFjdC5GQzx7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfT4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFtzZXR0aW5ncywgc2V0U2V0dGluZ3NdID0gdXNlU3RhdGU8VXNlclNldHRpbmdzPihnZXREZWZhdWx0U2V0dGluZ3MoKSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvLyBMb2FkIHNldHRpbmdzIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFNldHRpbmdzKCk7XG4gIH0sIFtdKTtcblxuICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGFwcGx5VGhlbWUgPSAoKSA9PiB7XG4gICAgICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgICAgXG4gICAgICAvLyBSZW1vdmUgZXhpc3RpbmcgdGhlbWUgY2xhc3Nlc1xuICAgICAgcm9vdC5jbGFzc0xpc3QucmVtb3ZlKCdsaWdodCcsICdkYXJrJyk7XG4gICAgICBcbiAgICAgIGlmIChzZXR0aW5ncy50aGVtZSA9PT0gJ2F1dG8nKSB7XG4gICAgICAgIC8vIFVzZSBzeXN0ZW0gcHJlZmVyZW5jZVxuICAgICAgICBjb25zdCBwcmVmZXJzRGFyayA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcztcbiAgICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKHByZWZlcnNEYXJrID8gJ2RhcmsnIDogJ2xpZ2h0Jyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBVc2UgZXhwbGljaXQgdGhlbWVcbiAgICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKHNldHRpbmdzLnRoZW1lKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgYXBwbHlUaGVtZSgpO1xuXG4gICAgLy8gTGlzdGVuIGZvciBzeXN0ZW0gdGhlbWUgY2hhbmdlcyB3aGVuIGluIGF1dG8gbW9kZVxuICAgIGlmIChzZXR0aW5ncy50aGVtZSA9PT0gJ2F1dG8nKSB7XG4gICAgICBjb25zdCBtZWRpYVF1ZXJ5ID0gd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKTtcbiAgICAgIGNvbnN0IGhhbmRsZUNoYW5nZSA9ICgpID0+IGFwcGx5VGhlbWUoKTtcbiAgICAgIG1lZGlhUXVlcnkuYWRkRXZlbnRMaXN0ZW5lcignY2hhbmdlJywgaGFuZGxlQ2hhbmdlKTtcbiAgICAgIFxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpO1xuICAgICAgfTtcbiAgICB9XG4gIH0sIFtzZXR0aW5ncy50aGVtZV0pO1xuXG4gIGNvbnN0IGxvYWRTZXR0aW5ncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgIGNvbnN0IGRlZmF1bHRzID0gZ2V0RGVmYXVsdFNldHRpbmdzKCk7XG5cbiAgICAgIC8vIExvYWQgZnJvbSBBUEkgdXNpbmcgU2V0dGluZ3NTZXJ2aWNlXG4gICAgICBjb25zdCBhcGlTZXR0aW5ncyA9IGF3YWl0IHNldHRpbmdzU2VydmljZS5nZXRTZXR0aW5ncygpO1xuICAgICAgY29uc3QgbWVyZ2VkU2V0dGluZ3MgPSB7XG4gICAgICAgIC4uLmRlZmF1bHRzLFxuICAgICAgICAuLi5hcGlTZXR0aW5ncyxcbiAgICAgICAgcHJlZmVycmVkTW9kZWxzOiB7XG4gICAgICAgICAgLi4uZGVmYXVsdHMucHJlZmVycmVkTW9kZWxzLFxuICAgICAgICAgIC4uLmFwaVNldHRpbmdzLnByZWZlcnJlZE1vZGVscyxcbiAgICAgICAgfSxcbiAgICAgICAgcHJvdmlkZXJTZXR0aW5nczoge1xuICAgICAgICAgIC4uLmRlZmF1bHRzLnByb3ZpZGVyU2V0dGluZ3MsXG4gICAgICAgICAgLi4uYXBpU2V0dGluZ3MucHJvdmlkZXJTZXR0aW5ncyxcbiAgICAgICAgICAvLyBBbHdheXMgb3ZlcnJpZGUgQVBJIGtleSB3aXRoIGVudmlyb25tZW50IHZhcmlhYmxlXG4gICAgICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19DSFVURVNfQVBJX1RPS0VOIHx8ICcnLFxuICAgICAgICB9LFxuICAgICAgICBhZ2VudFNldHRpbmdzOiB7XG4gICAgICAgICAgLi4uZGVmYXVsdHMuYWdlbnRTZXR0aW5ncyxcbiAgICAgICAgICAuLi5hcGlTZXR0aW5ncy5hZ2VudFNldHRpbmdzLFxuICAgICAgICB9LFxuICAgICAgICBvcmNoZXN0cmF0b3JTZXR0aW5nczoge1xuICAgICAgICAgIC4uLmRlZmF1bHRzLm9yY2hlc3RyYXRvclNldHRpbmdzLFxuICAgICAgICAgIC4uLmFwaVNldHRpbmdzLm9yY2hlc3RyYXRvclNldHRpbmdzLFxuICAgICAgICB9LFxuICAgICAgICB6ZXBTZXR0aW5nczoge1xuICAgICAgICAgIC4uLmRlZmF1bHRzLnplcFNldHRpbmdzLFxuICAgICAgICAgIC4uLmFwaVNldHRpbmdzLnplcFNldHRpbmdzLFxuICAgICAgICAgIC8vIEFsd2F5cyBvdmVycmlkZSBBUEkga2V5IHdpdGggZW52aXJvbm1lbnQgdmFyaWFibGVcbiAgICAgICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1pFUF9BUElfS0VZIHx8ICd5b3VyLWxvY2FsLXplcC1zZWNyZXQta2V5JyxcbiAgICAgICAgfSxcbiAgICAgIH07XG4gICAgICBzZXRTZXR0aW5ncyhtZXJnZWRTZXR0aW5ncyk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzZXR0aW5nczonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHNldHRpbmdzJyk7XG4gICAgICBzZXRTZXR0aW5ncyhnZXREZWZhdWx0U2V0dGluZ3MoKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVNldHRpbmdzID0gYXN5bmMgKHVwZGF0ZXM6IFBhcnRpYWw8VXNlclNldHRpbmdzPikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgLy8gRGVlcCBtZXJnZSB1cGRhdGVzIHdpdGggY3VycmVudCBzZXR0aW5nc1xuICAgICAgY29uc3QgbmV3U2V0dGluZ3MgPSB7XG4gICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICBwcmVmZXJyZWRNb2RlbHM6IHtcbiAgICAgICAgICAuLi5zZXR0aW5ncy5wcmVmZXJyZWRNb2RlbHMsXG4gICAgICAgICAgLi4udXBkYXRlcy5wcmVmZXJyZWRNb2RlbHMsXG4gICAgICAgIH0sXG4gICAgICAgIHByb3ZpZGVyU2V0dGluZ3M6IHtcbiAgICAgICAgICAuLi5zZXR0aW5ncy5wcm92aWRlclNldHRpbmdzLFxuICAgICAgICAgIC4uLnVwZGF0ZXMucHJvdmlkZXJTZXR0aW5ncyxcbiAgICAgICAgICAvLyBBbHdheXMga2VlcCBBUEkga2V5IGZyb20gZW52aXJvbm1lbnRcbiAgICAgICAgICBhcGlLZXk6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NIVVRFU19BUElfVE9LRU4gfHwgJycsXG4gICAgICAgIH0sXG4gICAgICAgIGFnZW50U2V0dGluZ3M6IHtcbiAgICAgICAgICAuLi5zZXR0aW5ncy5hZ2VudFNldHRpbmdzLFxuICAgICAgICAgIC4uLnVwZGF0ZXMuYWdlbnRTZXR0aW5ncyxcbiAgICAgICAgfSxcbiAgICAgICAgb3JjaGVzdHJhdG9yU2V0dGluZ3M6IHtcbiAgICAgICAgICAuLi5zZXR0aW5ncy5vcmNoZXN0cmF0b3JTZXR0aW5ncyxcbiAgICAgICAgICAuLi51cGRhdGVzLm9yY2hlc3RyYXRvclNldHRpbmdzLFxuICAgICAgICB9LFxuICAgICAgICB6ZXBTZXR0aW5nczoge1xuICAgICAgICAgIC4uLnNldHRpbmdzLnplcFNldHRpbmdzLFxuICAgICAgICAgIC4uLnVwZGF0ZXMuemVwU2V0dGluZ3MsXG4gICAgICAgICAgLy8gQWx3YXlzIGtlZXAgQVBJIGtleSBmcm9tIGVudmlyb25tZW50XG4gICAgICAgICAgYXBpS2V5OiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19aRVBfQVBJX0tFWSB8fCAneW91ci1sb2NhbC16ZXAtc2VjcmV0LWtleScsXG4gICAgICAgIH0sXG4gICAgICB9O1xuXG4gICAgICAvLyBGaWx0ZXIgb3V0IHNlbnNpdGl2ZSBkYXRhIGJlZm9yZSBzYXZpbmcgdG8gZGF0YWJhc2VcbiAgICAgIGNvbnN0IHNldHRpbmdzVG9TYXZlID0ge1xuICAgICAgICAuLi5uZXdTZXR0aW5ncyxcbiAgICAgICAgcHJvdmlkZXJTZXR0aW5nczoge1xuICAgICAgICAgIC4uLm5ld1NldHRpbmdzLnByb3ZpZGVyU2V0dGluZ3MsXG4gICAgICAgICAgYXBpS2V5OiAnJywgLy8gTmV2ZXIgc2F2ZSBBUEkga2V5c1xuICAgICAgICB9LFxuICAgICAgICB6ZXBTZXR0aW5nczoge1xuICAgICAgICAgIC4uLm5ld1NldHRpbmdzLnplcFNldHRpbmdzLFxuICAgICAgICAgIGFwaUtleTogJycsIC8vIE5ldmVyIHNhdmUgQVBJIGtleXNcbiAgICAgICAgfSxcbiAgICAgIH07XG5cbiAgICAgIC8vIFVwZGF0ZSBsb2NhbCBzdGF0ZSBpbW1lZGlhdGVseSBmb3IgcmVzcG9uc2l2ZSBVSSAod2l0aCBlbnZpcm9ubWVudCB2YXJpYWJsZXMpXG4gICAgICBzZXRTZXR0aW5ncyhuZXdTZXR0aW5ncyk7XG5cbiAgICAgIC8vIFNhdmUgdG8gQVBJICh3aXRob3V0IHNlbnNpdGl2ZSBkYXRhKVxuICAgICAgYXdhaXQgc2V0dGluZ3NTZXJ2aWNlLnVwZGF0ZVNldHRpbmdzKHNldHRpbmdzVG9TYXZlKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgc2V0dGluZ3M6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHNldHRpbmdzJyk7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlc2V0U2V0dGluZ3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgc2V0U2V0dGluZ3MoZ2V0RGVmYXVsdFNldHRpbmdzKCkpO1xuXG4gICAgICAvLyBSZXNldCBvbiBBUElcbiAgICAgIGF3YWl0IHNldHRpbmdzU2VydmljZS5yZXNldFNldHRpbmdzKCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcmVzZXQgc2V0dGluZ3M6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gcmVzZXQgc2V0dGluZ3MnKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWU6IFNldHRpbmdzQ29udGV4dFR5cGUgPSB7XG4gICAgc2V0dGluZ3MsXG4gICAgdXBkYXRlU2V0dGluZ3MsXG4gICAgcmVzZXRTZXR0aW5ncyxcbiAgICBpc0xvYWRpbmcsXG4gICAgZXJyb3JcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxTZXR0aW5nc0NvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1NldHRpbmdzQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbi8vIEhvb2sgdG8gdXNlIHNldHRpbmdzIGNvbnRleHRcbmV4cG9ydCBjb25zdCB1c2VTZXR0aW5ncyA9ICgpOiBTZXR0aW5nc0NvbnRleHRUeXBlID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoU2V0dGluZ3NDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlU2V0dGluZ3MgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFNldHRpbmdzUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNldHRpbmdzQ29udGV4dDsiXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0Iiwic2V0dGluZ3NTZXJ2aWNlIiwiZ2V0RGVmYXVsdFNldHRpbmdzIiwidGhlbWUiLCJkZWZhdWx0TW9kZSIsImF1dG9TYXZlIiwicHJlZmVycmVkTW9kZWxzIiwic2ltcGxlIiwiZW5oYW5jZWQiLCJvcmNoZXN0cmF0b3IiLCJyZWZpbmUiLCJtZW1vcnlTdW1tYXJpemF0aW9uIiwidGl0bGVHZW5lcmF0aW9uIiwicHJvdmlkZXJTZXR0aW5ncyIsImJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQ0hVVEVTX0FQSV9VUkwiLCJhcGlLZXkiLCJORVhUX1BVQkxJQ19DSFVURVNfQVBJX1RPS0VOIiwiYXZhaWxhYmxlTW9kZWxzIiwiYWdlbnRTZXR0aW5ncyIsIm1heEl0ZXJhdGlvbnMiLCJ0ZW1wZXJhdHVyZSIsImVuYWJsZVRvb2xzIiwib3JjaGVzdHJhdG9yU2V0dGluZ3MiLCJwYXJhbGxlbEFnZW50cyIsInRhc2tUaW1lb3V0IiwiYWdncmVnYXRpb25TdHJhdGVneSIsImVuYWJsZUNvbGxhYm9yYXRpb24iLCJ6ZXBTZXR0aW5ncyIsImVuYWJsZWQiLCJhcGlVcmwiLCJORVhUX1BVQkxJQ19aRVBfQVBJX1VSTCIsIk5FWFRfUFVCTElDX1pFUF9BUElfS0VZIiwiYXV0b01lbW9yeUV4dHJhY3Rpb24iLCJtZW1vcnlSZXRlbnRpb25EYXlzIiwic2hvd01lbW9yeUluc2lnaHRzIiwibWluRmFjdFJhdGluZyIsIlNldHRpbmdzQ29udGV4dCIsInVuZGVmaW5lZCIsIlNldHRpbmdzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldHRpbmdzIiwic2V0U2V0dGluZ3MiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwibG9hZFNldHRpbmdzIiwiYXBwbHlUaGVtZSIsInJvb3QiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsInJlbW92ZSIsInByZWZlcnNEYXJrIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJhZGQiLCJtZWRpYVF1ZXJ5IiwiaGFuZGxlQ2hhbmdlIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkZWZhdWx0cyIsImFwaVNldHRpbmdzIiwiZ2V0U2V0dGluZ3MiLCJtZXJnZWRTZXR0aW5ncyIsImVyciIsImNvbnNvbGUiLCJ1cGRhdGVTZXR0aW5ncyIsInVwZGF0ZXMiLCJuZXdTZXR0aW5ncyIsInNldHRpbmdzVG9TYXZlIiwicmVzZXRTZXR0aW5ncyIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VTZXR0aW5ncyIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/SettingsContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/device-id.ts":
/*!******************************!*\
  !*** ./src/lib/device-id.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDeviceId: () => (/* binding */ clearDeviceId),\n/* harmony export */   deviceIdService: () => (/* binding */ deviceIdService),\n/* harmony export */   getDeviceId: () => (/* binding */ getDeviceId),\n/* harmony export */   getDeviceIdInfo: () => (/* binding */ getDeviceIdInfo),\n/* harmony export */   migrateExistingUsers: () => (/* binding */ migrateExistingUsers)\n/* harmony export */ });\n/**\n * Unified Device ID Service\n * \n * Generates consistent device IDs that work across different ports\n * on the same hostname, ensuring conversation history is shared\n * between localhost:3000, localhost:3001, etc.\n */ // Browser fingerprinting for stable device identification\nclass DeviceIdService {\n    static getInstance() {\n        if (!DeviceIdService.instance) {\n            DeviceIdService.instance = new DeviceIdService();\n        }\n        return DeviceIdService.instance;\n    }\n    /**\n   * Get or generate a device ID that's consistent across ports\n   */ getDeviceId() {\n        if (this.deviceId) {\n            console.log('DeviceId: Using cached device ID:', this.deviceId);\n            return this.deviceId;\n        }\n        if (false) {}\n        // Client-side: try multiple strategies for consistency\n        console.log('DeviceId: Getting client-side device ID...');\n        this.deviceId = this.getOrCreateClientDeviceId();\n        console.log('DeviceId: Final device ID:', this.deviceId);\n        return this.deviceId;\n    }\n    /**\n   * Generate browser fingerprint for stable identification\n   */ getBrowserFingerprint() {\n        if (false) {}\n        return {\n            userAgent: navigator.userAgent,\n            language: navigator.language,\n            platform: navigator.platform,\n            screenResolution: \"\".concat(screen.width, \"x\").concat(screen.height),\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            hostname: window.location.hostname\n        };\n    }\n    /**\n   * Create a hash from browser fingerprint\n   */ hashFingerprint(fingerprint) {\n        const data = JSON.stringify(fingerprint);\n        let hash = 0;\n        for(let i = 0; i < data.length; i++){\n            const char = data.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32-bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\n   * Get or create device ID on client-side with multiple fallback strategies\n   */ getOrCreateClientDeviceId() {\n        const hostname = window.location.hostname;\n        // Strategy 1: Try hostname-specific storage first (new approach)\n        const hostnameKey = \"\".concat(this.HOSTNAME_STORAGE_KEY, \"-\").concat(hostname);\n        let deviceId = localStorage.getItem(hostnameKey);\n        if (deviceId) {\n            console.log('DeviceId: Using hostname-specific device ID:', deviceId);\n            return deviceId;\n        }\n        // Strategy 2: Try to migrate from old port-specific storage\n        const oldDeviceId = localStorage.getItem(this.STORAGE_KEY);\n        if (oldDeviceId) {\n            console.log('DeviceId: Found old port-specific device ID, creating unified device ID');\n            // Create a new unified device ID based on browser fingerprint\n            // This ensures all ports on the same hostname get the same device ID\n            try {\n                const fingerprint = this.getBrowserFingerprint();\n                const fingerprintHash = this.hashFingerprint(fingerprint);\n                const unifiedDeviceId = \"device-\".concat(hostname, \"-\").concat(fingerprintHash);\n                console.log('DeviceId: Generated unified device ID:', unifiedDeviceId);\n                // Store the unified device ID in hostname-specific storage\n                localStorage.setItem(hostnameKey, unifiedDeviceId);\n                // Keep the old one for backwards compatibility during transition\n                // but return the new unified one\n                return unifiedDeviceId;\n            } catch (error) {\n                console.warn('DeviceId: Failed to create unified device ID, using old one:', error);\n                localStorage.setItem(hostnameKey, oldDeviceId);\n                return oldDeviceId;\n            }\n        }\n        // Strategy 3: Generate from browser fingerprint (most stable)\n        try {\n            const fingerprint = this.getBrowserFingerprint();\n            const fingerprintHash = this.hashFingerprint(fingerprint);\n            // Don't include timestamp so same fingerprint always generates same ID\n            deviceId = \"device-\".concat(hostname, \"-\").concat(fingerprintHash);\n            console.log('DeviceId: Generated new fingerprint-based device ID:', deviceId);\n            // Store in both locations for compatibility\n            localStorage.setItem(hostnameKey, deviceId);\n            localStorage.setItem(this.STORAGE_KEY, deviceId);\n            return deviceId;\n        } catch (error) {\n            console.warn('DeviceId: Fingerprinting failed, using fallback:', error);\n        }\n        // Strategy 4: Fallback to random generation\n        deviceId = \"device-\".concat(hostname, \"-\").concat(Math.random().toString(36).substr(2, 9), \"-\").concat(Date.now());\n        console.log('DeviceId: Generated fallback device ID:', deviceId);\n        // Store in both locations\n        localStorage.setItem(hostnameKey, deviceId);\n        localStorage.setItem(this.STORAGE_KEY, deviceId);\n        return deviceId;\n    }\n    /**\n   * Clear device ID (for testing or reset purposes)\n   */ clearDeviceId() {\n        this.deviceId = null;\n        if (true) {\n            const hostname = window.location.hostname;\n            const hostnameKey = \"\".concat(this.HOSTNAME_STORAGE_KEY, \"-\").concat(hostname);\n            localStorage.removeItem(this.STORAGE_KEY);\n            localStorage.removeItem(hostnameKey);\n            console.log('DeviceId: Cleared device ID storage');\n        }\n    }\n    /**\n   * Get device ID info for debugging\n   */ getDeviceIdInfo() {\n        const deviceId = this.getDeviceId();\n        if (false) {}\n        try {\n            return {\n                deviceId,\n                hostname: window.location.hostname,\n                fingerprint: this.getBrowserFingerprint()\n            };\n        } catch (error) {\n            return {\n                deviceId,\n                hostname: window.location.hostname\n            };\n        }\n    }\n    /**\n   * Migrate existing users to new system\n   * This should be called during app initialization\n   */ async migrateExistingUsers() {\n        if (false) {}\n        const hostname = window.location.hostname;\n        const hostnameKey = \"\".concat(this.HOSTNAME_STORAGE_KEY, \"-\").concat(hostname);\n        console.log('DeviceId: Starting migration check...', {\n            hostname,\n            hostnameKey,\n            hasHostnameStorage: !!localStorage.getItem(hostnameKey),\n            hasOldStorage: !!localStorage.getItem(this.STORAGE_KEY)\n        });\n        // If hostname-specific storage already exists, no migration needed\n        if (localStorage.getItem(hostnameKey)) {\n            console.log('DeviceId: Migration not needed - hostname-specific storage already exists');\n            return;\n        }\n        // Check if old device ID exists\n        const oldDeviceId = localStorage.getItem(this.STORAGE_KEY);\n        if (oldDeviceId) {\n            console.log('DeviceId: Migrating existing device ID to hostname-specific storage:', oldDeviceId);\n            localStorage.setItem(hostnameKey, oldDeviceId);\n            console.log('DeviceId: Migration completed successfully');\n        } else {\n            console.log('DeviceId: No existing device ID found - will generate new one');\n        }\n    }\n    constructor(){\n        this.deviceId = null;\n        this.STORAGE_KEY = 'nexus-device-id';\n        this.HOSTNAME_STORAGE_KEY = 'nexus-hostname-device-id';\n    }\n}\n// Export singleton instance\nconst deviceIdService = DeviceIdService.getInstance();\n// Export convenience function for backwards compatibility\nconst getDeviceId = ()=>{\n    return deviceIdService.getDeviceId();\n};\n// Export for debugging\nconst getDeviceIdInfo = ()=>{\n    return deviceIdService.getDeviceIdInfo();\n};\n// Export for testing/reset\nconst clearDeviceId = ()=>{\n    deviceIdService.clearDeviceId();\n};\n// Export migration function\nconst migrateExistingUsers = async ()=>{\n    await deviceIdService.migrateExistingUsers();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/device-id.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/settings-service.ts":
/*!*************************************!*\
  !*** ./src/lib/settings-service.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   settingsService: () => (/* binding */ settingsService)\n/* harmony export */ });\n/* harmony import */ var _lib_device_id__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/device-id */ \"(app-pages-browser)/./src/lib/device-id.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n\nclass SettingsService {\n    // Get device ID using the unified service\n    getDeviceId() {\n        return (0,_lib_device_id__WEBPACK_IMPORTED_MODULE_0__.getDeviceId)();\n    }\n    // Get user settings\n    async getSettings() {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(\"/api/settings?userId=\".concat(encodeURIComponent(deviceId)), {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch settings: \".concat(response.statusText));\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error fetching settings:', error);\n            throw error;\n        }\n    }\n    // Update user settings\n    async updateSettings(settings) {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(\"/api/settings?userId=\".concat(encodeURIComponent(deviceId)), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update settings: \".concat(response.statusText));\n            }\n        } catch (error) {\n            console.error('Error updating settings:', error);\n            throw error;\n        }\n    }\n    // Reset settings to defaults\n    async resetSettings() {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(\"/api/settings?userId=\".concat(encodeURIComponent(deviceId)), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to reset settings: \".concat(response.statusText));\n            }\n        } catch (error) {\n            console.error('Error resetting settings:', error);\n            throw error;\n        }\n    }\n    // Discover available models from provider\n    async getAvailableModels(config) {\n        try {\n            const response = await fetch('/api/settings/models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    base_url: config.baseUrl,\n                    api_key: config.apiKey\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to discover models: \".concat(response.statusText));\n            }\n            const result = await response.json();\n            return {\n                success: result.success || false,\n                models: result.models || [],\n                total_count: result.total_count,\n                error: result.error\n            };\n        } catch (error) {\n            console.error('Error discovering models:', error);\n            return {\n                success: false,\n                models: [],\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Validate provider configuration\n    async validateProvider(config) {\n        try {\n            // Use model discovery as a way to validate the provider\n            const result = await this.getAvailableModels(config);\n            return {\n                valid: result.success,\n                error: result.error\n            };\n        } catch (error) {\n            return {\n                valid: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Get backend service status\n    async getServiceStatus() {\n        try {\n            const response = await fetch('/api/enhanced-chat/health', {\n                method: 'GET'\n            });\n            return {\n                available: response.ok,\n                error: response.ok ? undefined : \"Service unavailable: \".concat(response.statusText)\n            };\n        } catch (error) {\n            return {\n                available: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Export settings to JSON\n    exportSettings(settings) {\n        return JSON.stringify(settings, null, 2);\n    }\n    // Import settings from JSON\n    importSettings(jsonString) {\n        try {\n            const parsed = JSON.parse(jsonString);\n            // Validate the structure (basic validation)\n            if (typeof parsed !== 'object' || parsed === null) {\n                throw new Error('Invalid settings format');\n            }\n            return parsed;\n        } catch (error) {\n            throw new Error('Failed to parse settings JSON: ' + (error instanceof Error ? error.message : 'Unknown error'));\n        }\n    }\n    // Merge settings with defaults\n    mergeWithDefaults(settings, defaults) {\n        return {\n            ...defaults,\n            ...settings,\n            preferredModels: {\n                ...defaults.preferredModels,\n                ...settings.preferredModels\n            },\n            providerSettings: {\n                ...defaults.providerSettings,\n                ...settings.providerSettings\n            },\n            agentSettings: {\n                ...defaults.agentSettings,\n                ...settings.agentSettings\n            },\n            orchestratorSettings: {\n                ...defaults.orchestratorSettings,\n                ...settings.orchestratorSettings\n            }\n        };\n    }\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '';\n    }\n}\n// Export singleton instance\nconst settingsService = new SettingsService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (settingsService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/settings-service.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);