"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderPlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _SyncStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SyncStatus */ \"(app-pages-browser)/./src/components/SyncStatus.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { conversations, workspaces, currentConversationId, currentWorkspaceId, isOpen, onToggle, onNewChat, onSelectConversation, onDeleteConversation, onRenameConversation, onSelectWorkspace, onCreateWorkspace, onUpdateWorkspace, onDeleteWorkspace, onReorderWorkspaces, onAssignConversationToWorkspace, onOpenSettings } = param;\n    _s();\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hoveredId, setHoveredId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedWorkspaces, setExpandedWorkspaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showWorkspaceForm, setShowWorkspaceForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingWorkspaceId, setEditingWorkspaceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConversationMenu, setShowConversationMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedWorkspace, setDraggedWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverWorkspace, setDragOverWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredWorkspace, setHoveredWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedConversation, setDraggedConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverTarget, setDragOverTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [workspaceFormData, setWorkspaceFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#6366f1'\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    // Close conversation menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": ()=>{\n                    setShowConversationMenu(null);\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            if (showConversationMenu) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"Sidebar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"Sidebar.useEffect\"];\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        showConversationMenu\n    ]);\n    const handleStartEdit = (conversation)=>{\n        setEditingId(conversation.id);\n        setEditTitle(conversation.title);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingId && editTitle.trim()) {\n            onRenameConversation(editingId, editTitle.trim());\n        }\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const handleCancelEdit = ()=>{\n        setEditingId(null);\n        setEditTitle('');\n    };\n    // Workspace helper functions\n    const toggleWorkspace = (workspaceId)=>{\n        const newExpanded = new Set(expandedWorkspaces);\n        if (newExpanded.has(workspaceId)) {\n            newExpanded.delete(workspaceId);\n        } else {\n            newExpanded.add(workspaceId);\n        }\n        setExpandedWorkspaces(newExpanded);\n    };\n    const handleCreateWorkspace = ()=>{\n        if (workspaceFormData.name.trim()) {\n            onCreateWorkspace({\n                name: workspaceFormData.name.trim(),\n                description: workspaceFormData.description.trim() || undefined,\n                color: workspaceFormData.color\n            });\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n            setShowWorkspaceForm(false);\n        }\n    };\n    const handleUpdateWorkspace = ()=>{\n        if (editingWorkspaceId && workspaceFormData.name.trim()) {\n            const workspace = workspaces.find((w)=>w.id === editingWorkspaceId);\n            if (workspace) {\n                onUpdateWorkspace({\n                    ...workspace,\n                    name: workspaceFormData.name.trim(),\n                    description: workspaceFormData.description.trim() || undefined,\n                    color: workspaceFormData.color\n                });\n            }\n            setEditingWorkspaceId(null);\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n        }\n    };\n    const startEditWorkspace = (workspace)=>{\n        setEditingWorkspaceId(workspace.id);\n        setWorkspaceFormData({\n            name: workspace.name,\n            description: workspace.description || '',\n            color: workspace.color\n        });\n    };\n    const handleDeleteWorkspace = (workspaceId)=>{\n        if (confirm('Are you sure you want to delete this workspace? Conversations will be moved to unassigned.')) {\n            onDeleteWorkspace(workspaceId);\n        }\n    };\n    const handleCreateConversationInWorkspace = (workspaceId)=>{\n        // Create a new conversation and assign it to the workspace\n        onNewChat(workspaceId);\n    };\n    const handleAssignConversation = (conversationId, workspaceId)=>{\n        onAssignConversationToWorkspace(conversationId, workspaceId);\n        setShowConversationMenu(null);\n    };\n    // Drag and drop handlers for workspace reordering\n    const handleDragStart = (e, workspaceId)=>{\n        setDraggedWorkspace(workspaceId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', workspaceId);\n    };\n    const handleDragOver = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        e.dataTransfer.dropEffect = 'move';\n        if (draggedWorkspace && draggedWorkspace !== workspaceId) {\n            // Determine if we should insert above or below based on mouse position\n            const rect = e.currentTarget.getBoundingClientRect();\n            const midY = rect.top + rect.height / 2;\n            const insertAbove = e.clientY < midY;\n            setDragOverWorkspace(workspaceId);\n            // Store the insert position for visual feedback\n            e.currentTarget.dataset.insertAbove = insertAbove.toString();\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOverWorkspace(null);\n        // Clear the insert position\n        e.currentTarget.dataset.insertAbove = '';\n    };\n    const handleDragEnd = ()=>{\n        setDraggedWorkspace(null);\n        setDragOverWorkspace(null);\n        setDraggedConversation(null);\n        setDragOverTarget(null);\n    };\n    // Conversation drag and drop handlers\n    const handleConversationDragStart = (e, conversationId)=>{\n        setDraggedConversation(conversationId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', conversationId);\n    };\n    const handleWorkspaceDropZone = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (draggedConversation) {\n            setDragOverTarget({\n                type: workspaceId ? 'workspace' : 'unassigned',\n                id: workspaceId\n            });\n        }\n    };\n    const handleConversationDrop = async (e, targetWorkspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (!draggedConversation) return;\n        try {\n            await onAssignConversationToWorkspace(draggedConversation, targetWorkspaceId || null);\n        } catch (error) {\n            console.error('Failed to move conversation:', error);\n        } finally{\n            setDraggedConversation(null);\n            setDragOverTarget(null);\n        }\n    };\n    const handleDropBetween = async (insertIndex)=>{\n        if (!draggedWorkspace) {\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n            return;\n        }\n        try {\n            // Find the index of the dragged workspace\n            const draggedIndex = workspaces.findIndex((w)=>w.id === draggedWorkspace);\n            if (draggedIndex === -1) return;\n            console.log('Sidebar: handleDropBetween called', {\n                draggedWorkspace,\n                draggedIndex,\n                insertIndex,\n                totalWorkspaces: workspaces.length,\n                workspaceIds: workspaces.map((w)=>w.id)\n            });\n            // Adjust insert index if dragging down\n            let adjustedIndex = insertIndex;\n            if (draggedIndex < insertIndex) {\n                adjustedIndex = insertIndex - 1;\n            }\n            console.log('Sidebar: Adjusted index:', adjustedIndex);\n            // Check if the position is actually changing\n            if (draggedIndex === adjustedIndex) {\n                console.log('Sidebar: No position change needed, skipping reorder');\n                return;\n            }\n            // Create new order\n            const newWorkspaces = [\n                ...workspaces\n            ];\n            const [draggedItem] = newWorkspaces.splice(draggedIndex, 1);\n            newWorkspaces.splice(adjustedIndex, 0, draggedItem);\n            // Update positions and call reorder API\n            const workspaceIds = newWorkspaces.map((w)=>w.id);\n            console.log('Sidebar: Calling reorder with new order:', workspaceIds);\n            await onReorderWorkspaces(workspaceIds);\n        } catch (error) {\n            console.error('Failed to reorder workspaces:', error);\n        } finally{\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n        }\n    };\n    // Group conversations by workspace\n    const unassignedConversations = conversations.filter((conv)=>!conv.workspaceId);\n    const workspaceConversations = workspaces.reduce((acc, workspace)=>{\n        acc[workspace.id] = conversations.filter((conv)=>conv.workspaceId === workspace.id);\n        return acc;\n    }, {});\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) {\n            return 'Today';\n        } else if (diffInDays === 1) {\n            return 'Yesterday';\n        } else if (diffInDays < 7) {\n            return \"\".concat(diffInDays, \" days ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out\", \"w-96 md:w-80\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"md:relative md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Nexus\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"p-2 hover:bg-sidebar-accent rounded-lg md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewChat,\n                                className: \"w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Chat\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-sidebar-muted\",\n                                                    children: \"Workspaces\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowWorkspaceForm(true),\n                                                    className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                    title: \"Create workspace\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        showWorkspaceForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCreateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Create\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setShowWorkspaceForm(false);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        editingWorkspaceId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpdateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Update\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setEditingWorkspaceId(null);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sidebar-muted text-center py-8\",\n                                    children: \"No conversations yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === 'above-unassigned' && \"bg-blue-500 rounded\"),\n                                            onDragOver: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                setDragOverWorkspace('above-unassigned');\n                                            },\n                                            onDragLeave: ()=>setDragOverWorkspace(null),\n                                            onDrop: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleDropBetween(0); // Insert at the beginning\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 19\n                                        }, this),\n                                        unassignedConversations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors\", (dragOverTarget === null || dragOverTarget === void 0 ? void 0 : dragOverTarget.type) === 'unassigned' && \"bg-green-500/20 border-2 border-green-500 border-dashed\"),\n                                                    onDragOver: (e)=>draggedConversation && handleWorkspaceDropZone(e),\n                                                    onDrop: (e)=>draggedConversation && handleConversationDrop(e),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4 text-sidebar-muted\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-sidebar-muted\",\n                                                            children: \"Unassigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: [\n                                                                \"(\",\n                                                                unassignedConversations.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 ml-6\",\n                                                    children: unassignedConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative p-2 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\", draggedConversation === conversation.id && \"opacity-50\"),\n                                                            draggable: true,\n                                                            onDragStart: (e)=>handleConversationDragStart(e, conversation.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            onClick: ()=>onSelectConversation(conversation.id),\n                                                            onMouseEnter: ()=>setHoveredId(conversation.id),\n                                                            onMouseLeave: ()=>setHoveredId(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: editTitle,\n                                                                            onChange: (e)=>setEditTitle(e.target.value),\n                                                                            onBlur: handleSaveEdit,\n                                                                            onKeyDown: (e)=>{\n                                                                                if (e.key === 'Enter') handleSaveEdit();\n                                                                                if (e.key === 'Escape') handleCancelEdit();\n                                                                            },\n                                                                            className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                                            autoFocus: true,\n                                                                            onClick: (e)=>e.stopPropagation()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: conversation.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-sidebar-muted mt-1\",\n                                                                                    children: formatDate(conversation.updatedAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex gap-1 transition-opacity duration-200\", hoveredId === conversation.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleStartEdit(conversation);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                title: \"Edit conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 567,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            setShowConversationMenu(showConversationMenu === conversation.id ? null : conversation.id);\n                                                                                        },\n                                                                                        className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                        title: \"Move to workspace\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                            className: \"w-3 h-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 588,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 578,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    showConversationMenu === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: (e)=>{\n                                                                                                        e.stopPropagation();\n                                                                                                        handleAssignConversation(conversation.id, null);\n                                                                                                    },\n                                                                                                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors\",\n                                                                                                    children: \"\\uD83D\\uDCDD Unassigned\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                    lineNumber: 595,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                workspaces.map((ws)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                            handleAssignConversation(conversation.id, ws.id);\n                                                                                                        },\n                                                                                                        className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"w-2 h-2 rounded-full flex-shrink-0\",\n                                                                                                                style: {\n                                                                                                                    backgroundColor: ws.color\n                                                                                                                }\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                                lineNumber: 613,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this),\n                                                                                                            ws.name\n                                                                                                        ]\n                                                                                                    }, ws.id, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                        lineNumber: 605,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 594,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 593,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 577,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    onDeleteConversation(conversation.id);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors\",\n                                                                                title: \"Delete conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 632,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, conversation.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this),\n                                        workspaces.map((workspace, index)=>{\n                                            var _workspaceConversations_workspace_id;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    draggedWorkspace && draggedWorkspace !== workspace.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === \"above-\".concat(workspace.id) && \"bg-blue-500 rounded\"),\n                                                        onDragOver: (e)=>{\n                                                            e.preventDefault();\n                                                            e.stopPropagation();\n                                                            setDragOverWorkspace(\"above-\".concat(workspace.id));\n                                                        },\n                                                        onDragLeave: ()=>setDragOverWorkspace(null),\n                                                        onDrop: (e)=>{\n                                                            e.preventDefault();\n                                                            e.stopPropagation();\n                                                            handleDropBetween(index);\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex items-center gap-2 mb-2 p-2 rounded-lg transition-all duration-200 relative\", draggedConversation ? \"cursor-pointer\" : \"cursor-move\", draggedWorkspace === workspace.id && \"opacity-50 scale-95\", draggedWorkspace && draggedWorkspace !== workspace.id && \"opacity-75\", (dragOverTarget === null || dragOverTarget === void 0 ? void 0 : dragOverTarget.type) === 'workspace' && dragOverTarget.id === workspace.id && \"bg-green-500/20 border-2 border-green-500 border-dashed\"),\n                                                        draggable: !draggedConversation,\n                                                        onDragStart: (e)=>!draggedConversation && handleDragStart(e, workspace.id),\n                                                        onDragOver: (e)=>{\n                                                            if (draggedConversation) {\n                                                                handleWorkspaceDropZone(e, workspace.id);\n                                                            }\n                                                        },\n                                                        onDragLeave: (e)=>{\n                                                            if (draggedConversation) {\n                                                                setDragOverTarget(null);\n                                                            }\n                                                        },\n                                                        onDragEnd: handleDragEnd,\n                                                        onDrop: (e)=>{\n                                                            if (draggedConversation) {\n                                                                handleConversationDrop(e, workspace.id);\n                                                            }\n                                                        },\n                                                        onMouseEnter: ()=>{\n                                                            setHoveredWorkspace(workspace.id);\n                                                        },\n                                                        onMouseLeave: ()=>{\n                                                            setHoveredWorkspace(null);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleWorkspace(workspace.id),\n                                                                className: \"p-0.5 hover:bg-sidebar-accent rounded transition-colors\",\n                                                                children: expandedWorkspaces.has(workspace.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-sidebar-muted\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 704,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-sidebar-muted\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full flex-shrink-0\",\n                                                                style: {\n                                                                    backgroundColor: workspace.color\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-sidebar-foreground truncate\",\n                                                                children: workspace.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-sidebar-muted\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    ((_workspaceConversations_workspace_id = workspaceConversations[workspace.id]) === null || _workspaceConversations_workspace_id === void 0 ? void 0 : _workspaceConversations_workspace_id.length) || 0,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1 ml-auto\",\n                                                                draggable: false,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleCreateConversationInWorkspace(workspace.id),\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-green-500/20 rounded-md text-green-400 hover:text-green-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        title: \"New conversation in workspace\",\n                                                                        draggable: false,\n                                                                        onMouseDown: (e)=>e.stopPropagation(),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>startEditWorkspace(workspace),\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-sidebar-accent rounded-md transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        title: \"Edit workspace\",\n                                                                        draggable: false,\n                                                                        onMouseDown: (e)=>e.stopPropagation(),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDeleteWorkspace(workspace.id);\n                                                                        },\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        title: \"Delete workspace\",\n                                                                        draggable: false,\n                                                                        onMouseDown: (e)=>e.stopPropagation(),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expandedWorkspaces.has(workspace.id) && workspaceConversations[workspace.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 ml-6 mb-2\",\n                                                        children: workspaceConversations[workspace.id].map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative p-2 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\", draggedConversation === conversation.id && \"opacity-50\"),\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleConversationDragStart(e, conversation.id),\n                                                                onDragEnd: handleDragEnd,\n                                                                onClick: ()=>onSelectConversation(conversation.id),\n                                                                onMouseEnter: ()=>setHoveredId(conversation.id),\n                                                                onMouseLeave: ()=>setHoveredId(null),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: editTitle,\n                                                                                onChange: (e)=>setEditTitle(e.target.value),\n                                                                                onBlur: handleSaveEdit,\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter') handleSaveEdit();\n                                                                                    if (e.key === 'Escape') handleCancelEdit();\n                                                                                },\n                                                                                className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                                                autoFocus: true,\n                                                                                onClick: (e)=>e.stopPropagation()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 35\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-sm truncate\",\n                                                                                        children: conversation.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 798,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-sidebar-muted mt-1\",\n                                                                                        children: formatDate(conversation.updatedAt)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 801,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 781,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex gap-1 transition-opacity duration-200\", hoveredId === conversation.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleStartEdit(conversation);\n                                                                                    },\n                                                                                    className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                    title: \"Edit conversation\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 821,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                setShowConversationMenu(showConversationMenu === conversation.id ? null : conversation.id);\n                                                                                            },\n                                                                                            className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                            title: \"Move to workspace\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                className: \"w-3 h-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 834,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 824,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        showConversationMenu === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"py-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                            handleAssignConversation(conversation.id, null);\n                                                                                                        },\n                                                                                                        className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors\",\n                                                                                                        children: \"\\uD83D\\uDCDD Unassigned\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                        lineNumber: 841,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    workspaces.map((ws)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                            onClick: (e)=>{\n                                                                                                                e.stopPropagation();\n                                                                                                                handleAssignConversation(conversation.id, ws.id);\n                                                                                                            },\n                                                                                                            className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                    className: \"w-2 h-2 rounded-full flex-shrink-0\",\n                                                                                                                    style: {\n                                                                                                                        backgroundColor: ws.color\n                                                                                                                    }\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                                    lineNumber: 859,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this),\n                                                                                                                ws.name\n                                                                                                            ]\n                                                                                                        }, ws.id, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                            lineNumber: 851,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 840,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 839,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 823,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        onDeleteConversation(conversation.id);\n                                                                                    },\n                                                                                    className: \"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors\",\n                                                                                    title: \"Delete conversation\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 878,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 870,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, conversation.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, workspace.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 transition-all duration-200\", dragOverWorkspace === 'below-all' && \"bg-blue-500 rounded\"),\n                                            onDragOver: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                setDragOverWorkspace('below-all');\n                                            },\n                                            onDragLeave: ()=>setDragOverWorkspace(null),\n                                            onDrop: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleDropBetween(workspaces.length);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 p-3 border-t border-sidebar-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/files'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"File Manager\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, this),\n                                            settings.zepSettings.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/memory'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Memory Management\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onOpenSettings,\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Settings\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SyncStatus__WEBPACK_IMPORTED_MODULE_4__.SyncStatus, {\n                                        className: \"text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 954,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 950,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"fS+x1BkQ+GCvqupkBAwbiWVf/5M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});