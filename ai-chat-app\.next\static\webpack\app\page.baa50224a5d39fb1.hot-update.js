"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useEnhancedChat.ts":
/*!**************************************!*\
  !*** ./src/hooks/useEnhancedChat.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnhancedChat: () => (/* binding */ useEnhancedChat)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/enhanced-api */ \"(app-pages-browser)/./src/lib/enhanced-api.ts\");\n/* harmony import */ var _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/zep-service */ \"(app-pages-browser)/./src/lib/zep-service.ts\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useEnhancedChat auto */ \n\n\n\n\n\n\nfunction useEnhancedChat() {\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        conversations: [],\n        currentConversationId: null,\n        isLoading: false,\n        error: null,\n        sidebarOpen: false,\n        currentMode: 'simple',\n        progressStatus: undefined,\n        serviceHealth: null\n    });\n    // Initialize storage service, load conversations and check service health on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedChat.useEffect\": ()=>{\n            const initializeStorage = {\n                \"useEnhancedChat.useEffect.initializeStorage\": async ()=>{\n                    try {\n                        // Initialize the storage service\n                        await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.initialize();\n                        // Load conversations and current conversation ID\n                        const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                        const currentConversationId = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadCurrentConversationId();\n                        setState({\n                            \"useEnhancedChat.useEffect.initializeStorage\": (prev)=>({\n                                    ...prev,\n                                    conversations,\n                                    currentConversationId: conversations.find({\n                                        \"useEnhancedChat.useEffect.initializeStorage\": (c)=>c.id === currentConversationId\n                                    }[\"useEnhancedChat.useEffect.initializeStorage\"]) ? currentConversationId : null\n                                })\n                        }[\"useEnhancedChat.useEffect.initializeStorage\"]);\n                        // Check service health after initialization\n                        checkServiceHealth();\n                    } catch (error) {\n                        console.error('Failed to initialize storage:', error);\n                        // Fallback to basic localStorage loading\n                        const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                        const currentConversationId = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadCurrentConversationId();\n                        setState({\n                            \"useEnhancedChat.useEffect.initializeStorage\": (prev)=>({\n                                    ...prev,\n                                    conversations,\n                                    currentConversationId: conversations.find({\n                                        \"useEnhancedChat.useEffect.initializeStorage\": (c)=>c.id === currentConversationId\n                                    }[\"useEnhancedChat.useEffect.initializeStorage\"]) ? currentConversationId : null\n                                })\n                        }[\"useEnhancedChat.useEffect.initializeStorage\"]);\n                        // Still check service health\n                        checkServiceHealth();\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.initializeStorage\"];\n            initializeStorage();\n            // Cleanup on unmount\n            return ({\n                \"useEnhancedChat.useEffect\": ()=>{\n                // No cleanup needed for local storage\n                }\n            })[\"useEnhancedChat.useEffect\"];\n        }\n    }[\"useEnhancedChat.useEffect\"], []);\n    // Add periodic save and beforeunload save\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedChat.useEffect\": ()=>{\n            // Save conversations periodically (every 30 seconds)\n            const saveInterval = setInterval({\n                \"useEnhancedChat.useEffect.saveInterval\": async ()=>{\n                    try {\n                        if (state.conversations.length > 0) {\n                            console.log('Periodic save: Saving conversations to database');\n                            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveConversations(state.conversations);\n                            console.log('Periodic save: Successfully saved conversations');\n                        }\n                    } catch (error) {\n                        console.error('Periodic save failed:', error);\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.saveInterval\"], 30000);\n            // Save on page unload\n            const handleBeforeUnload = {\n                \"useEnhancedChat.useEffect.handleBeforeUnload\": async ()=>{\n                    try {\n                        if (state.conversations.length > 0) {\n                            console.log('Before unload: Saving conversations to database');\n                            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveConversations(state.conversations);\n                            console.log('Before unload: Successfully saved conversations');\n                        }\n                    } catch (error) {\n                        console.error('Before unload save failed:', error);\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"useEnhancedChat.useEffect\": ()=>{\n                    clearInterval(saveInterval);\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"useEnhancedChat.useEffect\"];\n        }\n    }[\"useEnhancedChat.useEffect\"], [\n        state.conversations\n    ]);\n    const checkServiceHealth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[checkServiceHealth]\": async ()=>{\n            try {\n                console.log('useEnhancedChat: Checking service health...');\n                const health = await _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__.EnhancedAPIService.checkServiceHealth();\n                console.log('useEnhancedChat: Service health received:', health);\n                setState({\n                    \"useEnhancedChat.useCallback[checkServiceHealth]\": (prev)=>({\n                            ...prev,\n                            serviceHealth: health\n                        })\n                }[\"useEnhancedChat.useCallback[checkServiceHealth]\"]);\n            } catch (error) {\n                console.error('useEnhancedChat: Failed to check service health:', error);\n                setState({\n                    \"useEnhancedChat.useCallback[checkServiceHealth]\": (prev)=>({\n                            ...prev,\n                            serviceHealth: null\n                        })\n                }[\"useEnhancedChat.useCallback[checkServiceHealth]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[checkServiceHealth]\"], []);\n    const getCurrentConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[getCurrentConversation]\": ()=>{\n            if (!state.currentConversationId) {\n                return null;\n            }\n            const found = state.conversations.find({\n                \"useEnhancedChat.useCallback[getCurrentConversation]\": (c)=>c.id === state.currentConversationId\n            }[\"useEnhancedChat.useCallback[getCurrentConversation]\"]) || null;\n            return found;\n        }\n    }[\"useEnhancedChat.useCallback[getCurrentConversation]\"], [\n        state.conversations,\n        state.currentConversationId\n    ]);\n    const createNewConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[createNewConversation]\": async (title, userId)=>{\n            const conversationId = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n            let zepSessionId;\n            // Create Zep session if service is configured and userId is provided\n            if (userId) {\n                try {\n                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                    if (zepService.isConfigured()) {\n                        zepSessionId = await zepService.addSession(conversationId, userId);\n                        console.log('Created Zep session:', zepSessionId);\n                    }\n                } catch (error) {\n                    console.warn('Failed to create Zep session:', error);\n                }\n            }\n            const newConversation = {\n                id: conversationId,\n                title: title || 'New Chat',\n                messages: [],\n                createdAt: Date.now(),\n                updatedAt: Date.now(),\n                userId,\n                zepSessionId,\n                facts: [],\n                entities: []\n            };\n            setState({\n                \"useEnhancedChat.useCallback[createNewConversation]\": (prev)=>({\n                        ...prev,\n                        conversations: [\n                            newConversation,\n                            ...prev.conversations\n                        ],\n                        currentConversationId: newConversation.id,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[createNewConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(newConversation);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(newConversation.id);\n            return newConversation.id;\n        }\n    }[\"useEnhancedChat.useCallback[createNewConversation]\"], []);\n    const selectConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[selectConversation]\": async (id)=>{\n            setState({\n                \"useEnhancedChat.useCallback[selectConversation]\": (prev)=>({\n                        ...prev,\n                        currentConversationId: id,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[selectConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(id);\n        }\n    }[\"useEnhancedChat.useCallback[selectConversation]\"], []);\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[deleteConversation]\": async (id)=>{\n            setState({\n                \"useEnhancedChat.useCallback[deleteConversation]\": (prev)=>{\n                    const newConversations = prev.conversations.filter({\n                        \"useEnhancedChat.useCallback[deleteConversation].newConversations\": (c)=>c.id !== id\n                    }[\"useEnhancedChat.useCallback[deleteConversation].newConversations\"]);\n                    const newCurrentId = prev.currentConversationId === id ? newConversations.length > 0 ? newConversations[0].id : null : prev.currentConversationId;\n                    return {\n                        ...prev,\n                        conversations: newConversations,\n                        currentConversationId: newCurrentId,\n                        error: null\n                    };\n                }\n            }[\"useEnhancedChat.useCallback[deleteConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.deleteConversation(id);\n        }\n    }[\"useEnhancedChat.useCallback[deleteConversation]\"], []);\n    const renameConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[renameConversation]\": async (id, newTitle)=>{\n            // Update local state immediately for responsive UI\n            setState({\n                \"useEnhancedChat.useCallback[renameConversation]\": (prev)=>({\n                        ...prev,\n                        conversations: prev.conversations.map({\n                            \"useEnhancedChat.useCallback[renameConversation]\": (c)=>c.id === id ? {\n                                    ...c,\n                                    title: newTitle,\n                                    updatedAt: Date.now()\n                                } : c\n                        }[\"useEnhancedChat.useCallback[renameConversation]\"])\n                    })\n            }[\"useEnhancedChat.useCallback[renameConversation]\"]);\n            try {\n                // Use the new dedicated rename method\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.renameConversation(id, newTitle);\n            } catch (error) {\n                console.error('Failed to rename conversation:', error);\n                // Revert the local state change on error\n                setState({\n                    \"useEnhancedChat.useCallback[renameConversation]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[renameConversation]\": (c)=>{\n                                    var _state_conversations_find;\n                                    return c.id === id ? {\n                                        ...c,\n                                        title: ((_state_conversations_find = state.conversations.find({\n                                            \"useEnhancedChat.useCallback[renameConversation]\": (conv)=>conv.id === id\n                                        }[\"useEnhancedChat.useCallback[renameConversation]\"])) === null || _state_conversations_find === void 0 ? void 0 : _state_conversations_find.title) || 'New Chat'\n                                    } : c;\n                                }\n                            }[\"useEnhancedChat.useCallback[renameConversation]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[renameConversation]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[renameConversation]\"], [\n        state.conversations\n    ]);\n    const setMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[setMode]\": (mode)=>{\n            console.log('🔄 MODE CHANGE: Setting mode to:', mode);\n            setState({\n                \"useEnhancedChat.useCallback[setMode]\": (prev)=>({\n                        ...prev,\n                        currentMode: mode\n                    })\n            }[\"useEnhancedChat.useCallback[setMode]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[setMode]\"], []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[sendMessage]\": async function(content, config) {\n            let isResend = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n            console.log('sendMessage called with content:', content, 'isResend:', isResend);\n            if (!content.trim()) {\n                console.log('Content is empty, returning');\n                return;\n            }\n            // Get current state values directly from state\n            let conversationId = state.currentConversationId;\n            let currentConversations = state.conversations;\n            let currentMode = state.currentMode;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const newConversation = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    title: content.slice(0, 50),\n                    messages: [],\n                    createdAt: Date.now(),\n                    updatedAt: Date.now()\n                };\n                console.log('Creating new conversation:', {\n                    newConversationId: newConversation.id,\n                    title: newConversation.title,\n                    prevConversationsCount: currentConversations.length\n                });\n                conversationId = newConversation.id;\n                currentConversations = [\n                    newConversation,\n                    ...currentConversations\n                ];\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(newConversation);\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(newConversation.id);\n                console.log('New conversation created and saved:', {\n                    conversationId,\n                    newConversationsCount: currentConversations.length,\n                    conversationIds: currentConversations.map({\n                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                });\n                setState({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                            ...prev,\n                            conversations: currentConversations,\n                            currentConversationId: conversationId,\n                            error: null\n                        })\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            }\n            console.log('🚀 SEND MESSAGE called with mode:', {\n                currentMode,\n                content: content.substring(0, 50),\n                config,\n                conversationId\n            });\n            // For resend operations, get the latest conversation state to ensure we have updated content\n            let conversation;\n            if (isResend) {\n                var _conversation_messages;\n                conversation = getCurrentConversation();\n                console.log('useEnhancedChat: Getting latest conversation for resend:', {\n                    conversationId,\n                    found: !!conversation,\n                    messageCount: (conversation === null || conversation === void 0 ? void 0 : (_conversation_messages = conversation.messages) === null || _conversation_messages === void 0 ? void 0 : _conversation_messages.length) || 0\n                });\n            } else {\n                conversation = currentConversations.find({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]) || null;\n                console.log('useEnhancedChat: Looking for conversation:', {\n                    conversationId,\n                    availableConversations: currentConversations.map({\n                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                    found: !!conversation\n                });\n            }\n            if (!conversation) {\n                console.error('Conversation not found:', conversationId);\n                return;\n            }\n            let userMessage;\n            let messagesToAdd;\n            if (isResend) {\n                // For resend, we assume the user message is already updated in the conversation\n                // We only need to add a new assistant message\n                const assistantMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: '',\n                    timestamp: Date.now(),\n                    isStreaming: true\n                };\n                // Use the content parameter for the user message (this contains the updated content)\n                // Get the last user message from conversation for metadata (id, timestamp, etc.)\n                const lastUserMessage = conversation.messages.filter({\n                    \"useEnhancedChat.useCallback[sendMessage].lastUserMessage\": (m)=>m.role === 'user'\n                }[\"useEnhancedChat.useCallback[sendMessage].lastUserMessage\"]).pop();\n                if (!lastUserMessage) {\n                    console.error('No user message found for resend');\n                    return;\n                }\n                // Create userMessage with updated content but preserve other properties\n                userMessage = {\n                    ...lastUserMessage,\n                    content: content.trim() // Use the updated content passed to sendMessage\n                };\n                messagesToAdd = [\n                    assistantMessage\n                ];\n            } else {\n                // Normal send: create both user and assistant messages\n                userMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'user',\n                    content: content.trim(),\n                    timestamp: Date.now()\n                };\n                const assistantMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: '',\n                    timestamp: Date.now(),\n                    isStreaming: true\n                };\n                messagesToAdd = [\n                    userMessage,\n                    assistantMessage\n                ];\n            }\n            // Add messages to conversation\n            const updatedConversation = {\n                ...conversation,\n                messages: [\n                    ...conversation.messages,\n                    ...messagesToAdd\n                ],\n                updatedAt: Date.now(),\n                title: conversation.messages.length === 0 ? content.slice(0, 50) : conversation.title\n            };\n            // Get the assistant message for later reference\n            const assistantMessage = messagesToAdd.find({\n                \"useEnhancedChat.useCallback[sendMessage].assistantMessage\": (m)=>m.role === 'assistant'\n            }[\"useEnhancedChat.useCallback[sendMessage].assistantMessage\"]);\n            if (!assistantMessage) {\n                console.error('No assistant message found in messagesToAdd');\n                return;\n            }\n            // Update state and save to storage immediately\n            setState({\n                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                    var _userMessage_content, _newState_conversations_find;\n                    console.log('setState: Adding messages to conversation', {\n                        conversationId,\n                        prevConversationsCount: prev.conversations.length,\n                        updatedConversationMessagesCount: updatedConversation.messages.length,\n                        userMessage: {\n                            id: userMessage.id,\n                            role: userMessage.role,\n                            content: (_userMessage_content = userMessage.content) === null || _userMessage_content === void 0 ? void 0 : _userMessage_content.substring(0, 50)\n                        },\n                        assistantMessage: {\n                            id: assistantMessage.id,\n                            role: assistantMessage.role,\n                            content: assistantMessage.content\n                        },\n                        isResend\n                    });\n                    const newState = {\n                        ...prev,\n                        conversations: prev.conversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? updatedConversation : c\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                        currentConversationId: conversationId,\n                        isLoading: true,\n                        error: null\n                    };\n                    console.log('setState: New state after adding messages', {\n                        conversationsCount: newState.conversations.length,\n                        currentConversationId: newState.currentConversationId,\n                        currentConversation: (_newState_conversations_find = newState.conversations.find({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"])) === null || _newState_conversations_find === void 0 ? void 0 : _newState_conversations_find.messages.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (m)=>{\n                                var _m_content;\n                                return {\n                                    id: m.id,\n                                    role: m.role,\n                                    contentLength: (_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length\n                                };\n                            }\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                    });\n                    return newState;\n                }\n            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            // Save updated conversation to storage with error handling\n            try {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (storageError) {\n                console.warn('Failed to save conversation to storage during sendMessage, but continuing:', storageError);\n            // Don't throw the error - the message was added to state successfully, so continue\n            }\n            // Messages added to state\n            try {\n                const messages = [\n                    ...conversation.messages,\n                    userMessage\n                ].map({\n                    \"useEnhancedChat.useCallback[sendMessage].messages\": (msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        })\n                }[\"useEnhancedChat.useCallback[sendMessage].messages\"]);\n                let fullResponse = '';\n                console.log(\"\\uD83D\\uDE80 SEND MESSAGE called with mode: \".concat(currentMode));\n                // Use appropriate API based on mode\n                if (currentMode === 'simple') {\n                    // Use original API service for simple mode\n                    const modelToUse = settings.preferredModels.simple;\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_2__.APIService.sendMessage(messages, {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onChunk\n                        (chunk)=>{\n                            fullResponse += chunk;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const newState = {\n                                        ...prev,\n                                        conversations: prev.conversations.map({\n                                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? {\n                                                    ...c,\n                                                    messages: c.messages.map({\n                                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>m.id === assistantMessage.id ? {\n                                                                ...m,\n                                                                content: fullResponse\n                                                            } : m\n                                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                                } : c\n                                        }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                    };\n                                    return newState;\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onComplete\n                        async ()=>{\n                            console.log('Simple mode message completed');\n                            // Check if this is the first user message (conversation had no messages before)\n                            const isFirstMessage = conversation.messages.length === 0;\n                            let finalConversationForZep;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse,\n                                                            isStreaming: false\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]),\n                                                updatedAt: Date.now()\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    finalConversationForZep = updatedConversations.find({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        isLoading: false\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                            // Save to storage with error handling\n                            if (finalConversationForZep) {\n                                try {\n                                    var _finalConversationForZep_messages__content, _finalConversationForZep_messages_;\n                                    console.log(\"Saving conversation after \".concat(currentMode, \" mode completion:\"), {\n                                        conversationId: finalConversationForZep.id,\n                                        messageCount: finalConversationForZep.messages.length,\n                                        lastMessageContent: (_finalConversationForZep_messages_ = finalConversationForZep.messages[finalConversationForZep.messages.length - 1]) === null || _finalConversationForZep_messages_ === void 0 ? void 0 : (_finalConversationForZep_messages__content = _finalConversationForZep_messages_.content) === null || _finalConversationForZep_messages__content === void 0 ? void 0 : _finalConversationForZep_messages__content.substring(0, 100)\n                                    });\n                                    await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                    console.log(\"Successfully saved conversation after \".concat(currentMode, \" mode completion\"));\n                                } catch (error) {\n                                    console.error(\"Failed to save conversation after \".concat(currentMode, \" mode completion:\"), error);\n                                    // Try to save again after a short delay\n                                    setTimeout({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": async ()=>{\n                                            try {\n                                                console.log(\"Retrying conversation save after \".concat(currentMode, \" mode completion\"));\n                                                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                                console.log(\"Successfully saved conversation on retry after \".concat(currentMode, \" mode completion\"));\n                                            } catch (retryError) {\n                                                console.error(\"Failed to save conversation on retry after \".concat(currentMode, \" mode completion:\"), retryError);\n                                            }\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"], 1000);\n                                }\n                            }\n                            // Generate title for first message (only for queries with more than 6 words)\n                            if (isFirstMessage && finalConversationForZep) {\n                                const wordCount = content.trim().split(/\\s+/).length;\n                                if (wordCount > 6) {\n                                    try {\n                                        var _settings_preferredModels;\n                                        const response = await fetch('/api/generate-title', {\n                                            method: 'POST',\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                userMessage: content.trim(),\n                                                model: (_settings_preferredModels = settings.preferredModels) === null || _settings_preferredModels === void 0 ? void 0 : _settings_preferredModels.titleGeneration\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            const data = await response.json();\n                                            const generatedTitle = data.title;\n                                            // Update conversation with generated title\n                                            setState({\n                                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                                    const updatedConversations = prev.conversations.map({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                                ...c,\n                                                                title: generatedTitle,\n                                                                updatedAt: Date.now()\n                                                            } : c\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                                    // Save updated conversation to storage\n                                                    const updatedConversation = updatedConversations.find({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversation\": (c)=>c.id === conversationId\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversation\"]);\n                                                    if (updatedConversation) {\n                                                        _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                                                    }\n                                                    return {\n                                                        ...prev,\n                                                        conversations: updatedConversations\n                                                    };\n                                                }\n                                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                        }\n                                    } catch (error) {\n                                        console.error('Failed to generate title:', error);\n                                    // Title generation failure is not critical, continue without it\n                                    }\n                                } else {\n                                    console.log(\"Skipping title generation: query has \".concat(wordCount, \" words (need >6)\"));\n                                }\n                            }\n                            // Send messages to Zep if configured\n                            if (finalConversationForZep === null || finalConversationForZep === void 0 ? void 0 : finalConversationForZep.zepSessionId) {\n                                try {\n                                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                                    if (zepService.isConfigured()) {\n                                        await zepService.addMessages(finalConversationForZep.zepSessionId, [\n                                            {\n                                                role: 'user',\n                                                content: userMessage.content\n                                            },\n                                            {\n                                                role: 'assistant',\n                                                content: fullResponse\n                                            }\n                                        ]);\n                                        console.log('Messages sent to Zep successfully');\n                                    }\n                                } catch (error) {\n                                    console.warn('Failed to send messages to Zep:', error);\n                                }\n                            }\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onError\n                        (error)=>{\n                            console.error('Simple mode API error:', error);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                                        ...prev,\n                                        conversations: prev.conversations.map({\n                                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? {\n                                                    ...c,\n                                                    messages: c.messages.filter({\n                                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>m.id !== assistantMessage.id\n                                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                                } : c\n                                        }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                                        isLoading: false,\n                                        error,\n                                        progressStatus: undefined\n                                    })\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], // model\n                    modelToUse);\n                } else {\n                    // Use enhanced API service for enhanced and orchestrator modes\n                    const enhancedConfig = {\n                        mode: currentMode === 'orchestrator' ? 'orchestrator' : 'single',\n                        maxIterations: (config === null || config === void 0 ? void 0 : config.maxIterations) || 3,\n                        parallelAgents: (config === null || config === void 0 ? void 0 : config.parallelAgents) || 3,\n                        timeout: (config === null || config === void 0 ? void 0 : config.timeout) || 300000,\n                        // Force tools to be enabled when in enhanced mode (user clicked Tools button)\n                        toolsEnabled: currentMode === 'enhanced' ? true : (config === null || config === void 0 ? void 0 : config.toolsEnabled) !== false\n                    };\n                    console.log(\"\\uD83D\\uDD27 Enhanced mode config:\", {\n                        mode: enhancedConfig.mode,\n                        toolsEnabled: enhancedConfig.toolsEnabled,\n                        currentMode,\n                        toolsForced: currentMode === 'enhanced'\n                    });\n                    await _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__.EnhancedAPIService.sendEnhancedMessage(messages, enhancedConfig, {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onChunk\n                        (chunk)=>{\n                            fullResponse += chunk;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        currentConversationId: conversationId // Maintain conversation ID during streaming\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onComplete\n                        async ()=>{\n                            console.log(\"\".concat(currentMode, \" mode message completed\"));\n                            // Check if this is the first user message (conversation had no messages before)\n                            const isFirstMessage = conversation.messages.length === 0;\n                            let finalConversationForZep;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse,\n                                                            isStreaming: false\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]),\n                                                updatedAt: Date.now()\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    finalConversationForZep = updatedConversations.find({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        isLoading: false\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                            // Save to storage with error handling\n                            if (finalConversationForZep) {\n                                try {\n                                    var _finalConversationForZep_messages__content, _finalConversationForZep_messages_;\n                                    console.log(\"Saving conversation after \".concat(currentMode, \" mode completion:\"), {\n                                        conversationId: finalConversationForZep.id,\n                                        messageCount: finalConversationForZep.messages.length,\n                                        lastMessageContent: (_finalConversationForZep_messages_ = finalConversationForZep.messages[finalConversationForZep.messages.length - 1]) === null || _finalConversationForZep_messages_ === void 0 ? void 0 : (_finalConversationForZep_messages__content = _finalConversationForZep_messages_.content) === null || _finalConversationForZep_messages__content === void 0 ? void 0 : _finalConversationForZep_messages__content.substring(0, 100)\n                                    });\n                                    await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                    console.log(\"Successfully saved conversation after \".concat(currentMode, \" mode completion\"));\n                                } catch (error) {\n                                    console.error(\"Failed to save conversation after \".concat(currentMode, \" mode completion:\"), error);\n                                    // Try to save again after a short delay\n                                    setTimeout({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": async ()=>{\n                                            try {\n                                                console.log(\"Retrying conversation save after \".concat(currentMode, \" mode completion\"));\n                                                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                                console.log(\"Successfully saved conversation on retry after \".concat(currentMode, \" mode completion\"));\n                                            } catch (retryError) {\n                                                console.error(\"Failed to save conversation on retry after \".concat(currentMode, \" mode completion:\"), retryError);\n                                            }\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"], 1000);\n                                }\n                            }\n                            // Generate title for first message\n                            if (isFirstMessage && finalConversationForZep) {\n                                try {\n                                    var _settings_preferredModels;\n                                    const response = await fetch('/api/generate-title', {\n                                        method: 'POST',\n                                        headers: {\n                                            'Content-Type': 'application/json'\n                                        },\n                                        body: JSON.stringify({\n                                            userMessage: content.trim(),\n                                            model: (_settings_preferredModels = settings.preferredModels) === null || _settings_preferredModels === void 0 ? void 0 : _settings_preferredModels.titleGeneration\n                                        })\n                                    });\n                                    if (response.ok) {\n                                        const data = await response.json();\n                                        const generatedTitle = data.title;\n                                        // Update conversation with generated title\n                                        setState({\n                                            \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                                const updatedConversations = prev.conversations.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                            ...c,\n                                                            title: generatedTitle,\n                                                            updatedAt: Date.now()\n                                                        } : c\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                                // Save updated conversation to storage\n                                                const updatedConversation = updatedConversations.find({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversation\": (c)=>c.id === conversationId\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversation\"]);\n                                                if (updatedConversation) {\n                                                    _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                                                }\n                                                return {\n                                                    ...prev,\n                                                    conversations: updatedConversations\n                                                };\n                                            }\n                                        }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                    }\n                                } catch (error) {\n                                    console.error('Failed to generate title:', error);\n                                // Title generation failure is not critical, continue without it\n                                }\n                            }\n                            // Send messages to Zep if configured\n                            if (finalConversationForZep === null || finalConversationForZep === void 0 ? void 0 : finalConversationForZep.zepSessionId) {\n                                try {\n                                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                                    if (zepService.isConfigured()) {\n                                        await zepService.addMessages(finalConversationForZep.zepSessionId, [\n                                            {\n                                                role: 'user',\n                                                content: userMessage.content\n                                            },\n                                            {\n                                                role: 'assistant',\n                                                content: fullResponse\n                                            }\n                                        ]);\n                                        console.log('Messages sent to Zep successfully');\n                                    }\n                                } catch (error) {\n                                    console.warn('Failed to send messages to Zep:', error);\n                                }\n                            }\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onError\n                        (error)=>{\n                            console.error(\"\".concat(currentMode, \" mode API error:\"), error);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.filter({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id !== assistantMessage.id\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        currentConversationId: conversationId,\n                                        isLoading: false,\n                                        error,\n                                        progressStatus: undefined\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onProgress (for orchestrator mode)\n                        (status)=>{\n                            console.log('Progress update:', status);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                                        ...prev,\n                                        progressStatus: status\n                                    })\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                }\n            } catch (error) {\n                console.error('Send message error (catch block):', error);\n                console.error('catch: Removing assistant message and updating state');\n                setState({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                        const updatedConversations = prev.conversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                    ...c,\n                                    messages: c.messages.filter({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id !== assistantMessage.id\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                } : c\n                        }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                        console.error('catch: Updated conversations after error:', updatedConversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>({\n                                    id: c.id,\n                                    messageCount: c.messages.length,\n                                    messages: c.messages.map({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>{\n                                            var _m_content;\n                                            return {\n                                                id: m.id,\n                                                role: m.role,\n                                                contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0\n                                            };\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                })\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"]));\n                        return {\n                            ...prev,\n                            conversations: updatedConversations,\n                            currentConversationId: conversationId,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'An error occurred',\n                            progressStatus: undefined\n                        };\n                    }\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[sendMessage]\"], [\n        state.currentConversationId,\n        state.conversations,\n        state.currentMode,\n        settings\n    ]);\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[toggleSidebar]\": ()=>{\n            setState({\n                \"useEnhancedChat.useCallback[toggleSidebar]\": (prev)=>({\n                        ...prev,\n                        sidebarOpen: !prev.sidebarOpen\n                    })\n            }[\"useEnhancedChat.useCallback[toggleSidebar]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[toggleSidebar]\"], []);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[clearError]\": ()=>{\n            setState({\n                \"useEnhancedChat.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[clearError]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[clearError]\"], []);\n    const editMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[editMessage]\": async (messageId, newContent)=>{\n            let updatedConversation;\n            setState({\n                \"useEnhancedChat.useCallback[editMessage]\": (prev)=>{\n                    const updatedConversations = prev.conversations.map({\n                        \"useEnhancedChat.useCallback[editMessage].updatedConversations\": (conversation)=>{\n                            if (conversation.id === prev.currentConversationId) {\n                                const updatedMessages = conversation.messages.map({\n                                    \"useEnhancedChat.useCallback[editMessage].updatedConversations.updatedMessages\": (message)=>message.id === messageId ? {\n                                            ...message,\n                                            content: newContent\n                                        } : message\n                                }[\"useEnhancedChat.useCallback[editMessage].updatedConversations.updatedMessages\"]);\n                                updatedConversation = {\n                                    ...conversation,\n                                    messages: updatedMessages,\n                                    updatedAt: Date.now()\n                                };\n                                return updatedConversation;\n                            }\n                            return conversation;\n                        }\n                    }[\"useEnhancedChat.useCallback[editMessage].updatedConversations\"]);\n                    return {\n                        ...prev,\n                        conversations: updatedConversations\n                    };\n                }\n            }[\"useEnhancedChat.useCallback[editMessage]\"]);\n            // Save to storage\n            if (updatedConversation) {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[editMessage]\"], []);\n    const resendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[resendMessage]\": async (messageId, updatedContent)=>{\n            console.log('resendMessage called with:', {\n                messageId,\n                updatedContent\n            });\n            const currentConversation = getCurrentConversation();\n            if (!currentConversation) {\n                console.log('No current conversation found');\n                return;\n            }\n            const messageIndex = currentConversation.messages.findIndex({\n                \"useEnhancedChat.useCallback[resendMessage].messageIndex\": (m)=>m.id === messageId\n            }[\"useEnhancedChat.useCallback[resendMessage].messageIndex\"]);\n            if (messageIndex === -1) {\n                console.log('Message not found in conversation');\n                return;\n            }\n            const userMessage = currentConversation.messages[messageIndex];\n            if (userMessage.role !== 'user') {\n                console.log('Message is not from user');\n                return;\n            }\n            // Use the updated content if provided, otherwise use the current message content\n            const contentToSend = updatedContent || userMessage.content;\n            console.log('Content to send:', contentToSend);\n            console.log('Original message content:', userMessage.content);\n            // Remove all messages after the edited user message\n            const messagesToKeep = currentConversation.messages.slice(0, messageIndex + 1);\n            // If we have updated content, update the message in the kept messages\n            if (updatedContent) {\n                messagesToKeep[messageIndex] = {\n                    ...userMessage,\n                    content: updatedContent,\n                    timestamp: Date.now() // Update timestamp to ensure object reference changes\n                };\n            }\n            const updatedConversation = {\n                ...currentConversation,\n                messages: messagesToKeep,\n                updatedAt: Date.now()\n            };\n            // STEP 1: Update the message content in the UI first\n            console.log('STEP 1: Updating message content in UI...');\n            setState({\n                \"useEnhancedChat.useCallback[resendMessage]\": (prev)=>{\n                    var _updatedConversation_messages_find_content, _updatedConversation_messages_find;\n                    const updatedConversations = prev.conversations.map({\n                        \"useEnhancedChat.useCallback[resendMessage].updatedConversations\": (c)=>c.id === currentConversation.id ? updatedConversation : c\n                    }[\"useEnhancedChat.useCallback[resendMessage].updatedConversations\"]);\n                    const newState = {\n                        ...prev,\n                        conversations: updatedConversations\n                    };\n                    console.log('STEP 1 COMPLETE: Message content updated in UI:', {\n                        messageId,\n                        updatedContent,\n                        messageInState: (_updatedConversation_messages_find = updatedConversation.messages.find({\n                            \"useEnhancedChat.useCallback[resendMessage]\": (m)=>m.id === messageId\n                        }[\"useEnhancedChat.useCallback[resendMessage]\"])) === null || _updatedConversation_messages_find === void 0 ? void 0 : (_updatedConversation_messages_find_content = _updatedConversation_messages_find.content) === null || _updatedConversation_messages_find_content === void 0 ? void 0 : _updatedConversation_messages_find_content.substring(0, 100),\n                        conversationId: currentConversation.id,\n                        totalMessages: updatedConversation.messages.length\n                    });\n                    return newState;\n                }\n            }[\"useEnhancedChat.useCallback[resendMessage]\"]);\n            // Save to storage with error handling\n            try {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (storageError) {\n                console.warn('Failed to save conversation to storage, but continuing with resend:', storageError);\n            // Don't throw the error - the UI update was successful, so continue with the resend\n            }\n            // STEP 2: Wait for UI to update, then send the new query\n            console.log('STEP 2: Waiting for UI update, then sending new query...');\n            await new Promise({\n                \"useEnhancedChat.useCallback[resendMessage]\": (resolve)=>setTimeout(resolve, 1000)\n            }[\"useEnhancedChat.useCallback[resendMessage]\"]); // Longer delay to ensure UI updates\n            // Resend the message with the correct content\n            console.log('STEP 2: Sending new query with content:', contentToSend);\n            await sendMessage(contentToSend, undefined, true);\n        }\n    }[\"useEnhancedChat.useCallback[resendMessage]\"], [\n        sendMessage,\n        getCurrentConversation\n    ]);\n    const deleteMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[deleteMessage]\": async (messageId)=>{\n            console.log('deleteMessage called with messageId:', messageId);\n            const currentConversation = getCurrentConversation();\n            if (!currentConversation) {\n                console.log('No current conversation found');\n                return;\n            }\n            const messageIndex = currentConversation.messages.findIndex({\n                \"useEnhancedChat.useCallback[deleteMessage].messageIndex\": (m)=>m.id === messageId\n            }[\"useEnhancedChat.useCallback[deleteMessage].messageIndex\"]);\n            if (messageIndex === -1) {\n                console.log('Message not found in conversation');\n                return;\n            }\n            try {\n                console.log('Calling StorageService.deleteMessage');\n                // Delete the message from the database\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.deleteMessage(currentConversation.id, messageId);\n                console.log('Message deleted from storage successfully');\n                // Update local state after successful deletion\n                const updatedMessages = currentConversation.messages.filter({\n                    \"useEnhancedChat.useCallback[deleteMessage].updatedMessages\": (m)=>m.id !== messageId\n                }[\"useEnhancedChat.useCallback[deleteMessage].updatedMessages\"]);\n                const updatedConversation = {\n                    ...currentConversation,\n                    messages: updatedMessages,\n                    updatedAt: Date.now()\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[deleteMessage]\": (prev)=>{\n                        const updatedConversations = prev.conversations.map({\n                            \"useEnhancedChat.useCallback[deleteMessage].updatedConversations\": (c)=>c.id === currentConversation.id ? updatedConversation : c\n                        }[\"useEnhancedChat.useCallback[deleteMessage].updatedConversations\"]);\n                        return {\n                            ...prev,\n                            conversations: updatedConversations\n                        };\n                    }\n                }[\"useEnhancedChat.useCallback[deleteMessage]\"]);\n            } catch (error) {\n                console.error('Failed to delete message:', error);\n            // Optionally show an error message to the user\n            }\n        }\n    }[\"useEnhancedChat.useCallback[deleteMessage]\"], [\n        getCurrentConversation\n    ]);\n    // Zep helper functions\n    const searchMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[searchMemory]\": async (userId, query, limit)=>{\n            try {\n                const response = await fetch('/api/zep/search', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId,\n                        query,\n                        limit: limit || 10\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error('Search failed');\n                }\n                const data = await response.json();\n                return data.results || [];\n            } catch (error) {\n                console.error('Memory search failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[searchMemory]\"], []);\n    const getMemoryContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[getMemoryContext]\": async (sessionId)=>{\n            try {\n                const response = await fetch(\"/api/zep/memory?sessionId=\".concat(sessionId));\n                if (!response.ok) {\n                    throw new Error('Failed to get memory context');\n                }\n                const data = await response.json();\n                return data.memory;\n            } catch (error) {\n                console.error('Failed to get memory context:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[getMemoryContext]\"], []);\n    const updateConversationInsights = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[updateConversationInsights]\": async (conversationId)=>{\n            const conversation = state.conversations.find({\n                \"useEnhancedChat.useCallback[updateConversationInsights].conversation\": (c)=>c.id === conversationId\n            }[\"useEnhancedChat.useCallback[updateConversationInsights].conversation\"]);\n            if (!(conversation === null || conversation === void 0 ? void 0 : conversation.zepSessionId)) return;\n            try {\n                const [factsResponse, entitiesResponse] = await Promise.all([\n                    fetch(\"/api/zep/facts?sessionId=\".concat(conversation.zepSessionId)),\n                    fetch(\"/api/zep/entities?sessionId=\".concat(conversation.zepSessionId))\n                ]);\n                const factsData = factsResponse.ok ? await factsResponse.json() : {\n                    facts: []\n                };\n                const entitiesData = entitiesResponse.ok ? await entitiesResponse.json() : {\n                    entities: []\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[updateConversationInsights]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[updateConversationInsights]\": (c)=>c.id === conversationId ? {\n                                        ...c,\n                                        facts: factsData.facts || [],\n                                        entities: entitiesData.entities || [],\n                                        updatedAt: Date.now()\n                                    } : c\n                            }[\"useEnhancedChat.useCallback[updateConversationInsights]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[updateConversationInsights]\"]);\n                // Update storage\n                const updatedConversation = {\n                    ...conversation,\n                    facts: factsData.facts || [],\n                    entities: entitiesData.entities || [],\n                    updatedAt: Date.now()\n                };\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (error) {\n                console.error('Failed to update conversation insights:', error);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[updateConversationInsights]\"], [\n        state.conversations\n    ]);\n    const reloadConversations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[reloadConversations]\": async ()=>{\n            try {\n                const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                console.log('Reloaded conversations:', conversations.map({\n                    \"useEnhancedChat.useCallback[reloadConversations]\": (c)=>({\n                            id: c.id,\n                            title: c.title,\n                            workspaceId: c.workspaceId\n                        })\n                }[\"useEnhancedChat.useCallback[reloadConversations]\"]));\n                console.log('Full conversation objects:', conversations);\n                setState({\n                    \"useEnhancedChat.useCallback[reloadConversations]\": (prev)=>({\n                            ...prev,\n                            conversations\n                        })\n                }[\"useEnhancedChat.useCallback[reloadConversations]\"]);\n            } catch (error) {\n                console.error('Failed to reload conversations:', error);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[reloadConversations]\"], []);\n    const enableZepForConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[enableZepForConversation]\": async function(conversationId) {\n            let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'default-user';\n            const conversation = state.conversations.find({\n                \"useEnhancedChat.useCallback[enableZepForConversation].conversation\": (c)=>c.id === conversationId\n            }[\"useEnhancedChat.useCallback[enableZepForConversation].conversation\"]);\n            if (!conversation || conversation.zepSessionId) {\n                return false; // Already has Zep or conversation not found\n            }\n            try {\n                const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                if (!zepService.isConfigured()) {\n                    console.warn('Zep service not configured');\n                    return false;\n                }\n                // Create Zep session\n                const zepSessionId = await zepService.addSession(conversationId, userId);\n                console.log('Created Zep session for existing conversation:', zepSessionId);\n                // Update conversation with Zep session ID and userId\n                const updatedConversation = {\n                    ...conversation,\n                    userId,\n                    zepSessionId,\n                    facts: [],\n                    entities: [],\n                    updatedAt: Date.now()\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[enableZepForConversation]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[enableZepForConversation]\": (c)=>c.id === conversationId ? updatedConversation : c\n                            }[\"useEnhancedChat.useCallback[enableZepForConversation]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[enableZepForConversation]\"]);\n                // Save to storage\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                // If there are existing messages, send them to Zep\n                if (conversation.messages.length > 0) {\n                    try {\n                        await zepService.addMessages(zepSessionId, conversation.messages.map({\n                            \"useEnhancedChat.useCallback[enableZepForConversation]\": (msg)=>({\n                                    role: msg.role,\n                                    content: msg.content\n                                })\n                        }[\"useEnhancedChat.useCallback[enableZepForConversation]\"]));\n                        console.log('Existing messages sent to Zep successfully');\n                    } catch (error) {\n                        console.warn('Failed to send existing messages to Zep:', error);\n                    }\n                }\n                return true;\n            } catch (error) {\n                console.error('Failed to enable Zep for conversation:', error);\n                return false;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[enableZepForConversation]\"], [\n        state.conversations\n    ]);\n    return {\n        ...state,\n        currentConversation: getCurrentConversation(),\n        createNewConversation,\n        selectConversation,\n        deleteConversation,\n        renameConversation,\n        sendMessage,\n        editMessage,\n        resendMessage,\n        deleteMessage,\n        setMode,\n        toggleSidebar,\n        clearError,\n        checkServiceHealth,\n        reloadConversations,\n        // Zep functions\n        searchMemory,\n        getMemoryContext,\n        updateConversationInsights,\n        enableZepForConversation\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useEnhancedChat.ts\n"));

/***/ })

});