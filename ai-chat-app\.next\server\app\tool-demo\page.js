/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/tool-demo/page";
exports.ids = ["app/tool-demo/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftool-demo%2Fpage&page=%2Ftool-demo%2Fpage&appPaths=%2Ftool-demo%2Fpage&pagePath=private-next-app-dir%2Ftool-demo%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftool-demo%2Fpage&page=%2Ftool-demo%2Fpage&appPaths=%2Ftool-demo%2Fpage&pagePath=private-next-app-dir%2Ftool-demo%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/tool-demo/page.tsx */ \"(rsc)/./src/app/tool-demo/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'tool-demo',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\tool-demo\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\tool-demo\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/tool-demo/page\",\n        pathname: \"/tool-demo\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/tool-demo/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftool-demo%2Fpage&page=%2Ftool-demo%2Fpage&appPaths=%2Ftool-demo%2Fpage&pagePath=private-next-app-dir%2Ftool-demo%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(rsc)/./src/contexts/SettingsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ToolTagDemo.tsx */ \"(rsc)/./src/components/ToolTagDemo.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VTRVIlNUMlNUNEZXYlNUMlNUNBSSU1QyU1Q0NoYXQlNUMlNUNjaGF0MTElNUMlNUNhaS1jaGF0LWFwcCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUb29sVGFnRGVtby50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVU0VSXFxcXERldlxcXFxBSVxcXFxDaGF0XFxcXGNoYXQxMVxcXFxhaS1jaGF0LWFwcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxUb29sVGFnRGVtby50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ece207ab3068\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERldlxcQUlcXENoYXRcXGNoYXQxMVxcYWktY2hhdC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVjZTIwN2FiMzA2OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(rsc)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(rsc)/./src/contexts/SettingsContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Nexus - AI Chat Assistant\",\n    description: \"A ChatGPT-like AI chat application with local conversation storage\",\n    icons: {\n        icon: '/favicon.svg'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_3__.SettingsProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/tool-demo/page.tsx":
/*!************************************!*\
  !*** ./src/app/tool-demo/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolDemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ToolTagDemo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ToolTagDemo */ \"(rsc)/./src/components/ToolTagDemo.tsx\");\n\n\nfunction ToolDemoPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolTagDemo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\tool-demo\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Rvb2wtZGVtby9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUVwQyxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsK0RBQVdBOzs7OztBQUNyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXZcXEFJXFxDaGF0XFxjaGF0MTFcXGFpLWNoYXQtYXBwXFxzcmNcXGFwcFxcdG9vbC1kZW1vXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVG9vbFRhZ0RlbW8gZnJvbSAnQC9jb21wb25lbnRzL1Rvb2xUYWdEZW1vJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVG9vbERlbW9QYWdlKCkge1xuICByZXR1cm4gPFRvb2xUYWdEZW1vIC8+O1xufVxuIl0sIm5hbWVzIjpbIlRvb2xUYWdEZW1vIiwiVG9vbERlbW9QYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/tool-demo/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ToolTagDemo.tsx":
/*!****************************************!*\
  !*** ./src/components/ToolTagDemo.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Dev\\AI\\Chat\\chat11\\ai-chat-app\\src\\components\\ToolTagDemo.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSettings: () => (/* binding */ useSettings)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const SettingsProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Dev\\AI\\Chat\\chat11\\ai-chat-app\\src\\contexts\\SettingsContext.tsx",
"SettingsProvider",
);const useSettings = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Dev\\AI\\Chat\\chat11\\ai-chat-app\\src\\contexts\\SettingsContext.tsx",
"useSettings",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\contexts\\\\SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Dev\\AI\\Chat\\chat11\\ai-chat-app\\src\\contexts\\SettingsContext.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(ssr)/./src/contexts/SettingsContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Cnode_modules%5C%5Ckatex%5C%5Cdist%5C%5Ckatex.min.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccontexts%5C%5CSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ToolTagDemo.tsx */ \"(ssr)/./src/components/ToolTagDemo.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VTRVIlNUMlNUNEZXYlNUMlNUNBSSU1QyU1Q0NoYXQlNUMlNUNjaGF0MTElNUMlNUNhaS1jaGF0LWFwcCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUb29sVGFnRGVtby50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBdUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVU0VSXFxcXERldlxcXFxBSVxcXFxDaGF0XFxcXGNoYXQxMVxcXFxhaS1jaGF0LWFwcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxUb29sVGFnRGVtby50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDev%5C%5CAI%5C%5CChat%5C%5Cchat11%5C%5Cai-chat-app%5C%5Csrc%5C%5Ccomponents%5C%5CToolTagDemo.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ToolTagDemo.tsx":
/*!****************************************!*\
  !*** ./src/components/ToolTagDemo.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolTagDemo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_toolTagParser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/toolTagParser */ \"(ssr)/./src/utils/toolTagParser.ts\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/./node_modules/rehype-raw/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst sampleToolTagContent = `Thailand and Cambodia have recently experienced intense border clashes, resulting in significant casualties and displacement. The conflict escalated after a landmine explosion injured five Thai soldiers in the disputed border area of Chong An Ma, Nam Yuen District, Ubon Ratchathani.\n\n<tool call>{\"name\": \"mark_task_complete\", \"arguments\": {\"summary\": \"Provided a comprehensive summary of the latest news regarding Thailand and Cambodia, focusing on recent border clashes, casualties, displacement, ceasefire agreements, and ongoing diplomatic efforts. Information was gathered from multiple credible news sources including Al Jazeera, Euronews, CBS News, and Anadolu Agency.\"}, \"final_message\": \"The latest situation between Thailand and Cambodia involves serious border conflicts, with casualties and mass displacement, but an immediate and unconditional ceasefire has been agreed upon following international mediation. Diplomatic talks are ongoing to ensure lasting peace. Let me know if you'd like updates on humanitarian efforts or historical context.\"}</tool call>\n\nTask completed. The latest developments between Thailand and Cambodia have been summarized, focusing on border clashes, casualties, displacement, and the recent ceasefire agreement.`;\nfunction ToolTagDemo() {\n    const [useCollapse, setUseCollapse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [customContent, setCustomContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(sampleToolTagContent);\n    const processedContent = useCollapse ? (0,_utils_toolTagParser__WEBPACK_IMPORTED_MODULE_2__.processToolTagsWithCollapse)(customContent) : (0,_utils_toolTagParser__WEBPACK_IMPORTED_MODULE_2__.processToolTags)(customContent);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-card border border-border rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-4 text-foreground\",\n                        children: \"Tool Tag Processing Demo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: useCollapse,\n                                            onChange: (e)=>setUseCollapse(e.target.checked),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-foreground\",\n                                            children: \"Use collapsible format\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-foreground mb-2\",\n                                        children: \"Test Content (edit to try different tool tags):\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: customContent,\n                                        onChange: (e)=>setCustomContent(e.target.value),\n                                        className: \"w-full h-40 p-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"Enter content with <tool>...</tool> tags...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card border border-border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold mb-4 text-foreground\",\n                                children: \"Raw Content\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-muted p-4 rounded-lg text-sm text-foreground overflow-x-auto whitespace-pre-wrap\",\n                                children: customContent\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-card border border-border rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-semibold mb-4 text-foreground\",\n                                children: \"Processed Output\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-muted p-4 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                                    remarkPlugins: [\n                                        remark_gfm__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                                    ],\n                                    rehypePlugins: [\n                                        rehype_raw__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n                                    ],\n                                    components: {\n                                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold mb-3 text-foreground\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, void 0),\n                                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-bold mb-2 text-foreground\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 19\n                                            }, void 0),\n                                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold mb-2 text-foreground\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 19\n                                            }, void 0),\n                                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-3 text-foreground leading-relaxed\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, void 0),\n                                        code: ({ inline, children, ...props })=>{\n                                            if (inline) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-background px-1 py-0.5 rounded text-sm font-mono text-foreground\",\n                                                    ...props,\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 23\n                                                }, void 0);\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"bg-background p-3 rounded overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"font-mono text-sm text-foreground\",\n                                                    ...props,\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 21\n                                            }, void 0);\n                                        },\n                                        details: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                                className: \"border border-border rounded-lg p-3 mb-3 bg-background\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, void 0),\n                                        summary: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"font-semibold cursor-pointer text-foreground hover:text-blue-600 transition-colors\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    },\n                                    children: processedContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-card border border-border rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4 text-foreground\",\n                        children: \"How it works\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"• The function looks for \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"<tool>...</tool>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 39\n                                    }, this),\n                                    \" tags in the message content\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• It parses the JSON content inside the tags\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• It formats the tool call information into readable markdown\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• With collapsible format, tool calls are wrapped in HTML details/summary elements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• The processed content is then rendered by ReactMarkdown with proper styling\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\ToolTagDemo.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ToolTagDemo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/settings-service */ \"(ssr)/./src/lib/settings-service.ts\");\n/* __next_internal_client_entry_do_not_use__ SettingsProvider,useSettings,default auto */ \n\n\n// Get default settings with environment variable fallbacks\nfunction getDefaultSettings() {\n    return {\n        theme: 'auto',\n        defaultMode: 'simple',\n        autoSave: true,\n        preferredModels: {\n            simple: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            enhanced: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            orchestrator: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            refine: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            memorySummarization: 'Qwen/Qwen3-235B-A22B-Instruct-2507',\n            titleGeneration: 'Qwen/Qwen3-235B-A22B-Instruct-2507'\n        },\n        providerSettings: {\n            baseUrl: \"https://llm.chutes.ai/v1\" || 0,\n            apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0,\n            availableModels: []\n        },\n        agentSettings: {\n            maxIterations: 5,\n            temperature: 0.7,\n            enableTools: [\n                'web_search',\n                'calculator',\n                'file_operations'\n            ]\n        },\n        orchestratorSettings: {\n            parallelAgents: 3,\n            taskTimeout: 300,\n            aggregationStrategy: 'consensus',\n            enableCollaboration: true\n        },\n        zepSettings: {\n            enabled: true,\n            apiUrl: \"http://localhost:8001\" || 0,\n            apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0,\n            autoMemoryExtraction: true,\n            memoryRetentionDays: 30,\n            showMemoryInsights: true,\n            minFactRating: 7\n        }\n    };\n}\n// Create context\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Settings provider component\nconst SettingsProvider = ({ children })=>{\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getDefaultSettings());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load settings on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            loadSettings();\n        }\n    }[\"SettingsProvider.useEffect\"], []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            const applyTheme = {\n                \"SettingsProvider.useEffect.applyTheme\": ()=>{\n                    const root = document.documentElement;\n                    // Remove existing theme classes\n                    root.classList.remove('light', 'dark');\n                    if (settings.theme === 'auto') {\n                        // Use system preference\n                        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        root.classList.add(prefersDark ? 'dark' : 'light');\n                    } else {\n                        // Use explicit theme\n                        root.classList.add(settings.theme);\n                    }\n                }\n            }[\"SettingsProvider.useEffect.applyTheme\"];\n            applyTheme();\n            // Listen for system theme changes when in auto mode\n            if (settings.theme === 'auto') {\n                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n                const handleChange = {\n                    \"SettingsProvider.useEffect.handleChange\": ()=>applyTheme()\n                }[\"SettingsProvider.useEffect.handleChange\"];\n                mediaQuery.addEventListener('change', handleChange);\n                return ({\n                    \"SettingsProvider.useEffect\": ()=>{\n                        mediaQuery.removeEventListener('change', handleChange);\n                    }\n                })[\"SettingsProvider.useEffect\"];\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings.theme\n    ]);\n    const loadSettings = async ()=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const defaults = getDefaultSettings();\n            // Load from API using SettingsService\n            const apiSettings = await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.getSettings();\n            const mergedSettings = {\n                ...defaults,\n                ...apiSettings,\n                preferredModels: {\n                    ...defaults.preferredModels,\n                    ...apiSettings.preferredModels\n                },\n                providerSettings: {\n                    ...defaults.providerSettings,\n                    ...apiSettings.providerSettings,\n                    // Always override API key with environment variable\n                    apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n                },\n                agentSettings: {\n                    ...defaults.agentSettings,\n                    ...apiSettings.agentSettings\n                },\n                orchestratorSettings: {\n                    ...defaults.orchestratorSettings,\n                    ...apiSettings.orchestratorSettings\n                },\n                zepSettings: {\n                    ...defaults.zepSettings,\n                    ...apiSettings.zepSettings,\n                    // Always override API key with environment variable\n                    apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0\n                }\n            };\n            setSettings(mergedSettings);\n        } catch (err) {\n            console.error('Failed to load settings:', err);\n            setError('Failed to load settings');\n            setSettings(getDefaultSettings());\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateSettings = async (updates)=>{\n        try {\n            setError(null);\n            // Deep merge updates with current settings\n            const newSettings = {\n                ...settings,\n                ...updates,\n                preferredModels: {\n                    ...settings.preferredModels,\n                    ...updates.preferredModels\n                },\n                providerSettings: {\n                    ...settings.providerSettings,\n                    ...updates.providerSettings,\n                    // Always keep API key from environment\n                    apiKey: \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0\n                },\n                agentSettings: {\n                    ...settings.agentSettings,\n                    ...updates.agentSettings\n                },\n                orchestratorSettings: {\n                    ...settings.orchestratorSettings,\n                    ...updates.orchestratorSettings\n                },\n                zepSettings: {\n                    ...settings.zepSettings,\n                    ...updates.zepSettings,\n                    // Always keep API key from environment\n                    apiKey: \"z_1dWlkIjoiYTY2YzRhYjYtYzAzZS00OWI2LWJlYzQtZDQxN2Q3MzI5MWUyIn0.Y9nPa8oAVDaWZ4t45baO4bSfeo4jhEDP1LbD2zWLAjP77j8819_K8GQPpjpV1hLu85ULtdwUFfYfUKN8NB2Qgw\" || 0\n                }\n            };\n            // Filter out sensitive data before saving to database\n            const settingsToSave = {\n                ...newSettings,\n                providerSettings: {\n                    ...newSettings.providerSettings,\n                    apiKey: ''\n                },\n                zepSettings: {\n                    ...newSettings.zepSettings,\n                    apiKey: ''\n                }\n            };\n            // Update local state immediately for responsive UI (with environment variables)\n            setSettings(newSettings);\n            // Save to API (without sensitive data)\n            await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.updateSettings(settingsToSave);\n        } catch (err) {\n            console.error('Failed to update settings:', err);\n            setError('Failed to update settings');\n            throw err;\n        }\n    };\n    const resetSettings = async ()=>{\n        try {\n            setError(null);\n            setSettings(getDefaultSettings());\n            // Reset on API\n            await _lib_settings_service__WEBPACK_IMPORTED_MODULE_2__.settingsService.resetSettings();\n        } catch (err) {\n            console.error('Failed to reset settings:', err);\n            setError('Failed to reset settings');\n            throw err;\n        }\n    };\n    const value = {\n        settings,\n        updateSettings,\n        resetSettings,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\contexts\\\\SettingsContext.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook to use settings context\nconst useSettings = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SettingsContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/device-id.ts":
/*!******************************!*\
  !*** ./src/lib/device-id.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearDeviceId: () => (/* binding */ clearDeviceId),\n/* harmony export */   deviceIdService: () => (/* binding */ deviceIdService),\n/* harmony export */   getDeviceId: () => (/* binding */ getDeviceId),\n/* harmony export */   getDeviceIdInfo: () => (/* binding */ getDeviceIdInfo),\n/* harmony export */   migrateExistingUsers: () => (/* binding */ migrateExistingUsers)\n/* harmony export */ });\n/**\n * Unified Device ID Service\n * \n * Generates consistent device IDs that work across different ports\n * on the same hostname, ensuring conversation history is shared\n * between localhost:3000, localhost:3001, etc.\n */ // Browser fingerprinting for stable device identification\nclass DeviceIdService {\n    constructor(){\n        this.deviceId = null;\n        this.STORAGE_KEY = 'nexus-device-id';\n        this.HOSTNAME_STORAGE_KEY = 'nexus-hostname-device-id';\n    }\n    static getInstance() {\n        if (!DeviceIdService.instance) {\n            DeviceIdService.instance = new DeviceIdService();\n        }\n        return DeviceIdService.instance;\n    }\n    /**\n   * Get or generate a device ID that's consistent across ports\n   */ getDeviceId() {\n        if (this.deviceId) {\n            console.log('DeviceId: Using cached device ID:', this.deviceId);\n            return this.deviceId;\n        }\n        if (true) {\n            // Server-side: generate a temporary device ID\n            this.deviceId = 'server-device-' + Date.now();\n            console.log('DeviceId: Generated server-side device ID:', this.deviceId);\n            return this.deviceId;\n        }\n        // Client-side: try multiple strategies for consistency\n        console.log('DeviceId: Getting client-side device ID...');\n        this.deviceId = this.getOrCreateClientDeviceId();\n        console.log('DeviceId: Final device ID:', this.deviceId);\n        return this.deviceId;\n    }\n    /**\n   * Generate browser fingerprint for stable identification\n   */ getBrowserFingerprint() {\n        if (true) {\n            throw new Error('Browser fingerprinting only available on client-side');\n        }\n        return {\n            userAgent: navigator.userAgent,\n            language: navigator.language,\n            platform: navigator.platform,\n            screenResolution: `${screen.width}x${screen.height}`,\n            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n            hostname: window.location.hostname\n        };\n    }\n    /**\n   * Create a hash from browser fingerprint\n   */ hashFingerprint(fingerprint) {\n        const data = JSON.stringify(fingerprint);\n        let hash = 0;\n        for(let i = 0; i < data.length; i++){\n            const char = data.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32-bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\n   * Get or create device ID on client-side with multiple fallback strategies\n   */ getOrCreateClientDeviceId() {\n        const hostname = window.location.hostname;\n        // Strategy 1: Try hostname-specific storage first (new approach)\n        const hostnameKey = `${this.HOSTNAME_STORAGE_KEY}-${hostname}`;\n        let deviceId = localStorage.getItem(hostnameKey);\n        if (deviceId) {\n            console.log('DeviceId: Using hostname-specific device ID:', deviceId);\n            return deviceId;\n        }\n        // Strategy 2: Try to migrate from old port-specific storage\n        const oldDeviceId = localStorage.getItem(this.STORAGE_KEY);\n        if (oldDeviceId) {\n            console.log('DeviceId: Found old port-specific device ID, creating unified device ID');\n            // Create a new unified device ID based on browser fingerprint\n            // This ensures all ports on the same hostname get the same device ID\n            try {\n                const fingerprint = this.getBrowserFingerprint();\n                const fingerprintHash = this.hashFingerprint(fingerprint);\n                const unifiedDeviceId = `device-${hostname}-${fingerprintHash}`;\n                console.log('DeviceId: Generated unified device ID:', unifiedDeviceId);\n                // Store the unified device ID in hostname-specific storage\n                localStorage.setItem(hostnameKey, unifiedDeviceId);\n                // Keep the old one for backwards compatibility during transition\n                // but return the new unified one\n                return unifiedDeviceId;\n            } catch (error) {\n                console.warn('DeviceId: Failed to create unified device ID, using old one:', error);\n                localStorage.setItem(hostnameKey, oldDeviceId);\n                return oldDeviceId;\n            }\n        }\n        // Strategy 3: Generate from browser fingerprint (most stable)\n        try {\n            const fingerprint = this.getBrowserFingerprint();\n            const fingerprintHash = this.hashFingerprint(fingerprint);\n            // Don't include timestamp so same fingerprint always generates same ID\n            deviceId = `device-${hostname}-${fingerprintHash}`;\n            console.log('DeviceId: Generated new fingerprint-based device ID:', deviceId);\n            // Store in both locations for compatibility\n            localStorage.setItem(hostnameKey, deviceId);\n            localStorage.setItem(this.STORAGE_KEY, deviceId);\n            return deviceId;\n        } catch (error) {\n            console.warn('DeviceId: Fingerprinting failed, using fallback:', error);\n        }\n        // Strategy 4: Fallback to random generation\n        deviceId = `device-${hostname}-${Math.random().toString(36).substr(2, 9)}-${Date.now()}`;\n        console.log('DeviceId: Generated fallback device ID:', deviceId);\n        // Store in both locations\n        localStorage.setItem(hostnameKey, deviceId);\n        localStorage.setItem(this.STORAGE_KEY, deviceId);\n        return deviceId;\n    }\n    /**\n   * Clear device ID (for testing or reset purposes)\n   */ clearDeviceId() {\n        this.deviceId = null;\n        if (false) {}\n    }\n    /**\n   * Get device ID info for debugging\n   */ getDeviceIdInfo() {\n        const deviceId = this.getDeviceId();\n        if (true) {\n            return {\n                deviceId\n            };\n        }\n        try {\n            return {\n                deviceId,\n                hostname: window.location.hostname,\n                fingerprint: this.getBrowserFingerprint()\n            };\n        } catch (error) {\n            return {\n                deviceId,\n                hostname: window.location.hostname\n            };\n        }\n    }\n    /**\n   * Migrate existing users to new system\n   * This should be called during app initialization\n   */ async migrateExistingUsers() {\n        if (true) {\n            console.log('DeviceId: Migration skipped - server-side');\n            return;\n        }\n        const hostname = window.location.hostname;\n        const hostnameKey = `${this.HOSTNAME_STORAGE_KEY}-${hostname}`;\n        console.log('DeviceId: Starting migration check...', {\n            hostname,\n            hostnameKey,\n            hasHostnameStorage: !!localStorage.getItem(hostnameKey),\n            hasOldStorage: !!localStorage.getItem(this.STORAGE_KEY)\n        });\n        // If hostname-specific storage already exists, no migration needed\n        if (localStorage.getItem(hostnameKey)) {\n            console.log('DeviceId: Migration not needed - hostname-specific storage already exists');\n            return;\n        }\n        // Check if old device ID exists\n        const oldDeviceId = localStorage.getItem(this.STORAGE_KEY);\n        if (oldDeviceId) {\n            console.log('DeviceId: Migrating existing device ID to hostname-specific storage:', oldDeviceId);\n            localStorage.setItem(hostnameKey, oldDeviceId);\n            console.log('DeviceId: Migration completed successfully');\n        } else {\n            console.log('DeviceId: No existing device ID found - will generate new one');\n        }\n    }\n}\n// Export singleton instance\nconst deviceIdService = DeviceIdService.getInstance();\n// Export convenience function for backwards compatibility\nconst getDeviceId = ()=>{\n    return deviceIdService.getDeviceId();\n};\n// Export for debugging\nconst getDeviceIdInfo = ()=>{\n    return deviceIdService.getDeviceIdInfo();\n};\n// Export for testing/reset\nconst clearDeviceId = ()=>{\n    deviceIdService.clearDeviceId();\n};\n// Export migration function\nconst migrateExistingUsers = async ()=>{\n    await deviceIdService.migrateExistingUsers();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/device-id.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/settings-service.ts":
/*!*************************************!*\
  !*** ./src/lib/settings-service.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   settingsService: () => (/* binding */ settingsService)\n/* harmony export */ });\n/* harmony import */ var _lib_device_id__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/device-id */ \"(ssr)/./src/lib/device-id.ts\");\n\nclass SettingsService {\n    constructor(){\n        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '';\n    }\n    // Get device ID using the unified service\n    getDeviceId() {\n        return (0,_lib_device_id__WEBPACK_IMPORTED_MODULE_0__.getDeviceId)();\n    }\n    // Get user settings\n    async getSettings() {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(`/api/settings?userId=${encodeURIComponent(deviceId)}`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to fetch settings: ${response.statusText}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('Error fetching settings:', error);\n            throw error;\n        }\n    }\n    // Update user settings\n    async updateSettings(settings) {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(`/api/settings?userId=${encodeURIComponent(deviceId)}`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to update settings: ${response.statusText}`);\n            }\n        } catch (error) {\n            console.error('Error updating settings:', error);\n            throw error;\n        }\n    }\n    // Reset settings to defaults\n    async resetSettings() {\n        try {\n            const deviceId = this.getDeviceId();\n            const response = await fetch(`/api/settings?userId=${encodeURIComponent(deviceId)}`, {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to reset settings: ${response.statusText}`);\n            }\n        } catch (error) {\n            console.error('Error resetting settings:', error);\n            throw error;\n        }\n    }\n    // Discover available models from provider\n    async getAvailableModels(config) {\n        try {\n            const response = await fetch('/api/settings/models', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    base_url: config.baseUrl,\n                    api_key: config.apiKey\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to discover models: ${response.statusText}`);\n            }\n            const result = await response.json();\n            return {\n                success: result.success || false,\n                models: result.models || [],\n                total_count: result.total_count,\n                error: result.error\n            };\n        } catch (error) {\n            console.error('Error discovering models:', error);\n            return {\n                success: false,\n                models: [],\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Validate provider configuration\n    async validateProvider(config) {\n        try {\n            // Use model discovery as a way to validate the provider\n            const result = await this.getAvailableModels(config);\n            return {\n                valid: result.success,\n                error: result.error\n            };\n        } catch (error) {\n            return {\n                valid: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Get backend service status\n    async getServiceStatus() {\n        try {\n            const response = await fetch('/api/enhanced-chat/health', {\n                method: 'GET'\n            });\n            return {\n                available: response.ok,\n                error: response.ok ? undefined : `Service unavailable: ${response.statusText}`\n            };\n        } catch (error) {\n            return {\n                available: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Export settings to JSON\n    exportSettings(settings) {\n        return JSON.stringify(settings, null, 2);\n    }\n    // Import settings from JSON\n    importSettings(jsonString) {\n        try {\n            const parsed = JSON.parse(jsonString);\n            // Validate the structure (basic validation)\n            if (typeof parsed !== 'object' || parsed === null) {\n                throw new Error('Invalid settings format');\n            }\n            return parsed;\n        } catch (error) {\n            throw new Error('Failed to parse settings JSON: ' + (error instanceof Error ? error.message : 'Unknown error'));\n        }\n    }\n    // Merge settings with defaults\n    mergeWithDefaults(settings, defaults) {\n        return {\n            ...defaults,\n            ...settings,\n            preferredModels: {\n                ...defaults.preferredModels,\n                ...settings.preferredModels\n            },\n            providerSettings: {\n                ...defaults.providerSettings,\n                ...settings.providerSettings\n            },\n            agentSettings: {\n                ...defaults.agentSettings,\n                ...settings.agentSettings\n            },\n            orchestratorSettings: {\n                ...defaults.orchestratorSettings,\n                ...settings.orchestratorSettings\n            }\n        };\n    }\n}\n// Export singleton instance\nconst settingsService = new SettingsService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (settingsService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/settings-service.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/toolTagParser.ts":
/*!************************************!*\
  !*** ./src/utils/toolTagParser.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatToolCall: () => (/* binding */ formatToolCall),\n/* harmony export */   parseToolTag: () => (/* binding */ parseToolTag),\n/* harmony export */   processToolTags: () => (/* binding */ processToolTags),\n/* harmony export */   processToolTagsWithCollapse: () => (/* binding */ processToolTagsWithCollapse)\n/* harmony export */ });\n/**\n * Utility functions for parsing and formatting <tool> tags in AI responses\n */ /**\n * Parses a <tool> tag content and extracts the JSON data\n */ function parseToolTag(toolContent) {\n    try {\n        // Remove any extra whitespace and newlines\n        const cleanContent = toolContent.trim();\n        // Try to parse as JSON\n        const parsed = JSON.parse(cleanContent);\n        // Validate that it has the expected structure\n        if (typeof parsed === 'object' && parsed !== null) {\n            return {\n                name: parsed.name || 'unknown',\n                arguments: parsed.arguments || {},\n                summary: parsed.summary,\n                final_message: parsed.final_message\n            };\n        }\n        return null;\n    } catch (error) {\n        console.warn('Failed to parse tool tag content:', error);\n        return null;\n    }\n}\n/**\n * Formats a tool call into a readable markdown representation\n */ function formatToolCall(toolCall) {\n    const { name, arguments: args, summary, final_message } = toolCall;\n    let formatted = `### 🔧 Tool Call: ${name}\\n\\n`;\n    // Add summary if available\n    if (summary) {\n        formatted += `**Summary:** ${summary}\\n\\n`;\n    }\n    // Add arguments if they exist and are not empty\n    if (args && Object.keys(args).length > 0) {\n        formatted += `**Arguments:**\\n`;\n        formatted += '```json\\n';\n        formatted += JSON.stringify(args, null, 2);\n        formatted += '\\n```\\n\\n';\n    }\n    // Add final message if available\n    if (final_message) {\n        formatted += `**Result:** ${final_message}\\n\\n`;\n    }\n    return formatted;\n}\n/**\n * Processes message content to replace <tool> tags with formatted content\n */ function processToolTags(content) {\n    // Regular expression to match <tool>...</tool> tags (including variations like <tool call>)\n    const toolTagRegex = /<tool[^>]*>([\\s\\S]*?)<\\/tool[^>]*>/gi;\n    return content.replace(toolTagRegex, (match, toolContent)=>{\n        const toolCall = parseToolTag(toolContent);\n        if (toolCall) {\n            return formatToolCall(toolCall);\n        } else {\n            // If parsing fails, wrap in a code block to make it more readable\n            return `\\n\\`\\`\\`\\n${toolContent.trim()}\\n\\`\\`\\`\\n`;\n        }\n    });\n}\n/**\n * Alternative processing that creates collapsible sections for tool calls\n */ function processToolTagsWithCollapse(content) {\n    const toolTagRegex = /<tool[^>]*>([\\s\\S]*?)<\\/tool[^>]*>/gi;\n    return content.replace(toolTagRegex, (match, toolContent)=>{\n        const toolCall = parseToolTag(toolContent);\n        if (toolCall) {\n            const { name, arguments: args, summary, final_message } = toolCall;\n            let formatted = `<details>\\n<summary><strong>🔧 Tool Call: ${name}</strong>`;\n            if (summary) {\n                formatted += ` - ${summary}`;\n            }\n            formatted += `</summary>\\n\\n`;\n            if (args && Object.keys(args).length > 0) {\n                formatted += `**Arguments:**\\n\\`\\`\\`json\\n${JSON.stringify(args, null, 2)}\\n\\`\\`\\`\\n\\n`;\n            }\n            if (final_message) {\n                formatted += `**Result:** ${final_message}\\n\\n`;\n            }\n            formatted += `</details>\\n\\n`;\n            return formatted;\n        } else {\n            // If parsing fails, wrap in a collapsible code block\n            return `<details>\\n<summary><strong>🔧 Tool Output</strong></summary>\\n\\n\\`\\`\\`\\n${toolContent.trim()}\\n\\`\\`\\`\\n\\n</details>\\n\\n`;\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/toolTagParser.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/katex","vendor-chunks/parse5","vendor-chunks/micromark-core-commonmark","vendor-chunks/entities","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark","vendor-chunks/hast-util-to-parse5","vendor-chunks/unified","vendor-chunks/mdast-util-to-markdown","vendor-chunks/character-entities","vendor-chunks/property-information","vendor-chunks/mdast-util-from-markdown","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-raw","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/micromark-util-subtokenize","vendor-chunks/react-markdown","vendor-chunks/unist-util-visit-parents","vendor-chunks/hastscript","vendor-chunks/markdown-table","vendor-chunks/unist-util-visit","vendor-chunks/hast-util-from-parse5","vendor-chunks/unist-util-is","vendor-chunks/vfile-message","vendor-chunks/mdast-util-gfm-table","vendor-chunks/@ungap","vendor-chunks/micromark-util-character","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/devlop","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/remark-rehype","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/inline-style-parser","vendor-chunks/trough","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/zwitch","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/micromark-factory-label","vendor-chunks/extend","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-factory-title","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-to-string","vendor-chunks/micromark-util-chunked","vendor-chunks/hast-util-parse-selector","vendor-chunks/vfile-location","vendor-chunks/style-to-js","vendor-chunks/unist-util-stringify-position","vendor-chunks/micromark-extension-gfm","vendor-chunks/unist-util-position","vendor-chunks/dequal","vendor-chunks/mdast-util-gfm","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/comma-separated-tokens","vendor-chunks/trim-lines","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/micromark-util-decode-string","vendor-chunks/style-to-object","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-factory-whitespace","vendor-chunks/remark-gfm","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/remark-parse","vendor-chunks/mdast-util-phrasing","vendor-chunks/micromark-util-classify-character","vendor-chunks/hast-util-whitespace","vendor-chunks/longest-streak","vendor-chunks/micromark-util-resolve-all","vendor-chunks/rehype-raw","vendor-chunks/micromark-util-encode","vendor-chunks/html-url-attributes","vendor-chunks/decode-named-character-reference","vendor-chunks/ccount","vendor-chunks/space-separated-tokens","vendor-chunks/web-namespaces","vendor-chunks/is-plain-obj","vendor-chunks/has-flag","vendor-chunks/html-void-elements","vendor-chunks/bail"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ftool-demo%2Fpage&page=%2Ftool-demo%2Fpage&appPaths=%2Ftool-demo%2Fpage&pagePath=private-next-app-dir%2Ftool-demo%2Fpage.tsx&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();