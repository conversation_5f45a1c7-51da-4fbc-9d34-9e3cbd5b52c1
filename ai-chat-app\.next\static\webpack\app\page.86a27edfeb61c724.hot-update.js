"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FolderPlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChatBubbleLeftIcon,ChevronDownIcon,ChevronRightIcon,EllipsisVerticalIcon,FolderPlusIcon,PencilIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,FileText,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _SyncStatus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SyncStatus */ \"(app-pages-browser)/./src/components/SyncStatus.tsx\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { conversations, workspaces, currentConversationId, currentWorkspaceId, isOpen, onToggle, onNewChat, onSelectConversation, onDeleteConversation, onRenameConversation, onSelectWorkspace, onCreateWorkspace, onUpdateWorkspace, onDeleteWorkspace, onReorderWorkspaces, onAssignConversationToWorkspace, onOpenSettings } = param;\n    _s();\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editTitle, setEditTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hoveredId, setHoveredId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedWorkspaces, setExpandedWorkspaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showWorkspaceForm, setShowWorkspaceForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingWorkspaceId, setEditingWorkspaceId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showConversationMenu, setShowConversationMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedWorkspace, setDraggedWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverWorkspace, setDragOverWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredWorkspace, setHoveredWorkspace] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedConversation, setDraggedConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverTarget, setDragOverTarget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [workspaceFormData, setWorkspaceFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        description: '',\n        color: '#6366f1'\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    // Close conversation menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": ()=>{\n                    setShowConversationMenu(null);\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            if (showConversationMenu) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"Sidebar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"Sidebar.useEffect\"];\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        showConversationMenu\n    ]);\n    const handleStartEdit = (conversation)=>{\n        setEditingId(conversation.id);\n        setEditTitle(conversation.title);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingId && editTitle.trim()) {\n            onRenameConversation(editingId, editTitle.trim());\n        }\n        setEditingId(null);\n        setEditTitle('');\n    };\n    const handleCancelEdit = ()=>{\n        setEditingId(null);\n        setEditTitle('');\n    };\n    // Workspace helper functions\n    const toggleWorkspace = (workspaceId)=>{\n        const newExpanded = new Set(expandedWorkspaces);\n        if (newExpanded.has(workspaceId)) {\n            newExpanded.delete(workspaceId);\n        } else {\n            newExpanded.add(workspaceId);\n        }\n        setExpandedWorkspaces(newExpanded);\n    };\n    const handleCreateWorkspace = ()=>{\n        if (workspaceFormData.name.trim()) {\n            onCreateWorkspace({\n                name: workspaceFormData.name.trim(),\n                description: workspaceFormData.description.trim() || undefined,\n                color: workspaceFormData.color\n            });\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n            setShowWorkspaceForm(false);\n        }\n    };\n    const handleUpdateWorkspace = ()=>{\n        if (editingWorkspaceId && workspaceFormData.name.trim()) {\n            const workspace = workspaces.find((w)=>w.id === editingWorkspaceId);\n            if (workspace) {\n                onUpdateWorkspace({\n                    ...workspace,\n                    name: workspaceFormData.name.trim(),\n                    description: workspaceFormData.description.trim() || undefined,\n                    color: workspaceFormData.color\n                });\n            }\n            setEditingWorkspaceId(null);\n            setWorkspaceFormData({\n                name: '',\n                description: '',\n                color: '#6366f1'\n            });\n        }\n    };\n    const startEditWorkspace = (workspace)=>{\n        setEditingWorkspaceId(workspace.id);\n        setWorkspaceFormData({\n            name: workspace.name,\n            description: workspace.description || '',\n            color: workspace.color\n        });\n    };\n    const handleDeleteWorkspace = (workspaceId)=>{\n        if (confirm('Are you sure you want to delete this workspace? Conversations will be moved to unassigned.')) {\n            onDeleteWorkspace(workspaceId);\n        }\n    };\n    const handleCreateConversationInWorkspace = (workspaceId)=>{\n        // Create a new conversation and assign it to the workspace\n        onNewChat(workspaceId);\n    };\n    const handleAssignConversation = (conversationId, workspaceId)=>{\n        onAssignConversationToWorkspace(conversationId, workspaceId);\n        setShowConversationMenu(null);\n    };\n    // Drag and drop handlers for workspace reordering\n    const handleDragStart = (e, workspaceId)=>{\n        setDraggedWorkspace(workspaceId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', workspaceId);\n    };\n    const handleDragOver = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        e.dataTransfer.dropEffect = 'move';\n        if (draggedWorkspace && draggedWorkspace !== workspaceId) {\n            // Determine if we should insert above or below based on mouse position\n            const rect = e.currentTarget.getBoundingClientRect();\n            const midY = rect.top + rect.height / 2;\n            const insertAbove = e.clientY < midY;\n            setDragOverWorkspace(workspaceId);\n            // Store the insert position for visual feedback\n            e.currentTarget.dataset.insertAbove = insertAbove.toString();\n        }\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOverWorkspace(null);\n        // Clear the insert position\n        e.currentTarget.dataset.insertAbove = '';\n    };\n    const handleDragEnd = ()=>{\n        setDraggedWorkspace(null);\n        setDragOverWorkspace(null);\n        setDraggedConversation(null);\n        setDragOverTarget(null);\n    };\n    // Conversation drag and drop handlers\n    const handleConversationDragStart = (e, conversationId)=>{\n        setDraggedConversation(conversationId);\n        e.dataTransfer.effectAllowed = 'move';\n        e.dataTransfer.setData('text/plain', conversationId);\n    };\n    const handleWorkspaceDropZone = (e, workspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (draggedConversation) {\n            setDragOverTarget({\n                type: workspaceId ? 'workspace' : 'unassigned',\n                id: workspaceId\n            });\n        }\n    };\n    const handleConversationDrop = async (e, targetWorkspaceId)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        if (!draggedConversation) return;\n        try {\n            await onAssignConversationToWorkspace(draggedConversation, targetWorkspaceId || null);\n        } catch (error) {\n            console.error('Failed to move conversation:', error);\n        } finally{\n            setDraggedConversation(null);\n            setDragOverTarget(null);\n        }\n    };\n    const handleDropBetween = async (insertIndex)=>{\n        if (!draggedWorkspace) {\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n            return;\n        }\n        try {\n            // Find the index of the dragged workspace\n            const draggedIndex = workspaces.findIndex((w)=>w.id === draggedWorkspace);\n            if (draggedIndex === -1) return;\n            console.log('Sidebar: handleDropBetween called', {\n                draggedWorkspace,\n                draggedIndex,\n                insertIndex,\n                totalWorkspaces: workspaces.length,\n                workspaceIds: workspaces.map((w)=>w.id)\n            });\n            // Adjust insert index if dragging down\n            let adjustedIndex = insertIndex;\n            if (draggedIndex < insertIndex) {\n                adjustedIndex = insertIndex - 1;\n            }\n            console.log('Sidebar: Adjusted index:', adjustedIndex);\n            // Check if the position is actually changing\n            if (draggedIndex === adjustedIndex) {\n                console.log('Sidebar: No position change needed, skipping reorder');\n                return;\n            }\n            // Create new order\n            const newWorkspaces = [\n                ...workspaces\n            ];\n            const [draggedItem] = newWorkspaces.splice(draggedIndex, 1);\n            newWorkspaces.splice(adjustedIndex, 0, draggedItem);\n            // Update positions and call reorder API\n            const workspaceIds = newWorkspaces.map((w)=>w.id);\n            console.log('Sidebar: Calling reorder with new order:', workspaceIds);\n            await onReorderWorkspaces(workspaceIds);\n        } catch (error) {\n            console.error('Failed to reorder workspaces:', error);\n        } finally{\n            setDraggedWorkspace(null);\n            setDragOverWorkspace(null);\n        }\n    };\n    // Group conversations by workspace\n    const unassignedConversations = conversations.filter((conv)=>!conv.workspaceId);\n    const workspaceConversations = workspaces.reduce((acc, workspace)=>{\n        acc[workspace.id] = conversations.filter((conv)=>conv.workspaceId === workspace.id);\n        return acc;\n    }, {});\n    const formatDate = (timestamp)=>{\n        const date = new Date(timestamp);\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) {\n            return 'Today';\n        } else if (diffInDays === 1) {\n            return 'Yesterday';\n        } else if (diffInDays < 7) {\n            return \"\".concat(diffInDays, \" days ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out\", \"w-96 md:w-80\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", \"md:relative md:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold\",\n                                    children: \"Nexus\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    className: \"p-2 hover:bg-sidebar-accent rounded-lg md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onNewChat,\n                                className: \"w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"New Chat\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto px-4 pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-sidebar-muted\",\n                                                    children: \"Workspaces\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowWorkspaceForm(true),\n                                                    className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                    title: \"Create workspace\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        showWorkspaceForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCreateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Create\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setShowWorkspaceForm(false);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        editingWorkspaceId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-3 p-3 bg-sidebar-accent/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Workspace name\",\n                                                    value: workspaceFormData.name,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Description (optional)\",\n                                                    value: workspaceFormData.description,\n                                                    onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    className: \"w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"color\",\n                                                            value: workspaceFormData.color,\n                                                            onChange: (e)=>setWorkspaceFormData((prev)=>({\n                                                                        ...prev,\n                                                                        color: e.target.value\n                                                                    })),\n                                                            className: \"w-8 h-8 rounded border border-sidebar-border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: \"Color\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUpdateWorkspace,\n                                                            className: \"px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90\",\n                                                            children: \"Update\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setEditingWorkspaceId(null);\n                                                                setWorkspaceFormData({\n                                                                    name: '',\n                                                                    description: '',\n                                                                    color: '#6366f1'\n                                                                });\n                                                            },\n                                                            className: \"px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80\",\n                                                            children: \"Cancel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                workspaces.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-sidebar-muted mb-3 px-1\",\n                                            children: \"My Workspaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === 'above-first-workspace' && \"bg-blue-500 rounded\"),\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        setDragOverWorkspace('above-first-workspace');\n                                                    },\n                                                    onDragLeave: ()=>setDragOverWorkspace(null),\n                                                    onDrop: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleDropBetween(0); // Insert at the beginning\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this),\n                                                workspaces.map((workspace, index)=>{\n                                                    var _workspaceConversations_workspace_id;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            draggedWorkspace && draggedWorkspace !== workspace.id && index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 -mb-1 transition-all duration-200\", dragOverWorkspace === \"above-\".concat(workspace.id) && \"bg-blue-500 rounded\"),\n                                                                onDragOver: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.stopPropagation();\n                                                                    setDragOverWorkspace(\"above-\".concat(workspace.id));\n                                                                },\n                                                                onDragLeave: ()=>setDragOverWorkspace(null),\n                                                                onDrop: (e)=>{\n                                                                    e.preventDefault();\n                                                                    e.stopPropagation();\n                                                                    handleDropBetween(index);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group flex items-center gap-2 p-2 rounded-lg transition-all duration-200 relative cursor-move\", draggedWorkspace === workspace.id && \"opacity-50 scale-95\", draggedWorkspace && draggedWorkspace !== workspace.id && \"opacity-75\"),\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleDragStart(e, workspace.id),\n                                                                onDragEnd: handleDragEnd,\n                                                                onMouseEnter: ()=>setHoveredWorkspace(workspace.id),\n                                                                onMouseLeave: ()=>setHoveredWorkspace(null),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: workspace.color\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-sidebar-foreground truncate\",\n                                                                        children: workspace.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-sidebar-muted\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            ((_workspaceConversations_workspace_id = workspaceConversations[workspace.id]) === null || _workspaceConversations_workspace_id === void 0 ? void 0 : _workspaceConversations_workspace_id.length) || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex gap-1 ml-auto\",\n                                                                        draggable: false,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleCreateConversationInWorkspace(workspace.id),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-green-500/20 rounded-md text-green-400 hover:text-green-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"New conversation in workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>startEditWorkspace(workspace),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-sidebar-accent rounded-md transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"Edit workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 569,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 559,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleDeleteWorkspace(workspace.id),\n                                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-all duration-200\", hoveredWorkspace === workspace.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                                title: \"Delete workspace\",\n                                                                                draggable: false,\n                                                                                onMouseDown: (e)=>e.stopPropagation(),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, workspace.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                }),\n                                                draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 transition-all duration-200\", dragOverWorkspace === 'below-all-workspaces' && \"bg-blue-500 rounded\"),\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        setDragOverWorkspace('below-all-workspaces');\n                                                    },\n                                                    onDragLeave: ()=>setDragOverWorkspace(null),\n                                                    onDrop: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleDropBetween(workspaces.length);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sidebar-muted text-center py-8\",\n                                    children: \"No conversations yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-sidebar-muted mb-3 px-1\",\n                                            children: \"Conversations\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this),\n                                        unassignedConversations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors\", (dragOverTarget === null || dragOverTarget === void 0 ? void 0 : dragOverTarget.type) === 'unassigned' && \"bg-green-500/20 border-2 border-green-500 border-dashed\"),\n                                                    onDragOver: (e)=>draggedConversation && handleWorkspaceDropZone(e),\n                                                    onDrop: (e)=>draggedConversation && handleConversationDrop(e),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 text-sidebar-muted\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-sidebar-muted\",\n                                                            children: \"Unassigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-sidebar-muted\",\n                                                            children: [\n                                                                \"(\",\n                                                                unassignedConversations.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 624,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 ml-6\",\n                                                    children: unassignedConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative p-2 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\", draggedConversation === conversation.id && \"opacity-50\"),\n                                                            draggable: true,\n                                                            onDragStart: (e)=>handleConversationDragStart(e, conversation.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            onClick: ()=>onSelectConversation(conversation.id),\n                                                            onMouseEnter: ()=>setHoveredId(conversation.id),\n                                                            onMouseLeave: ()=>setHoveredId(null),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: editTitle,\n                                                                            onChange: (e)=>setEditTitle(e.target.value),\n                                                                            onBlur: handleSaveEdit,\n                                                                            onKeyDown: (e)=>{\n                                                                                if (e.key === 'Enter') handleSaveEdit();\n                                                                                if (e.key === 'Escape') handleCancelEdit();\n                                                                            },\n                                                                            className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                                            autoFocus: true,\n                                                                            onClick: (e)=>e.stopPropagation()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: conversation.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 672,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-sidebar-muted mt-1\",\n                                                                                    children: formatDate(conversation.updatedAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 655,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex gap-1 transition-opacity duration-200\", hoveredId === conversation.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    handleStartEdit(conversation);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                title: \"Edit conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 695,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            setShowConversationMenu(showConversationMenu === conversation.id ? null : conversation.id);\n                                                                                        },\n                                                                                        className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                        title: \"Move to workspace\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"w-3 h-3\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 708,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 698,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    showConversationMenu === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"py-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: (e)=>{\n                                                                                                        e.stopPropagation();\n                                                                                                        handleAssignConversation(conversation.id, null);\n                                                                                                    },\n                                                                                                    className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors\",\n                                                                                                    children: \"\\uD83D\\uDCDD Unassigned\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                    lineNumber: 715,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                workspaces.map((ws)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                            handleAssignConversation(conversation.id, ws.id);\n                                                                                                        },\n                                                                                                        className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"w-2 h-2 rounded-full flex-shrink-0\",\n                                                                                                                style: {\n                                                                                                                    backgroundColor: ws.color\n                                                                                                                }\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                                lineNumber: 733,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this),\n                                                                                                            ws.name\n                                                                                                        ]\n                                                                                                    }, ws.id, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                        lineNumber: 725,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 714,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 713,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: (e)=>{\n                                                                                    e.stopPropagation();\n                                                                                    onDeleteConversation(conversation.id);\n                                                                                },\n                                                                                className: \"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors\",\n                                                                                title: \"Delete conversation\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"w-3 h-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, conversation.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this),\n                                        workspaces.map((workspace)=>{\n                                            var _workspaceConversations_workspace_id;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors\", (dragOverTarget === null || dragOverTarget === void 0 ? void 0 : dragOverTarget.type) === 'workspace' && dragOverTarget.id === workspace.id && \"bg-green-500/20 border-2 border-green-500 border-dashed\"),\n                                                        onDragOver: (e)=>{\n                                                            if (draggedConversation) {\n                                                                handleWorkspaceDropZone(e, workspace.id);\n                                                            }\n                                                        },\n                                                        onDragLeave: (e)=>{\n                                                            if (draggedConversation) {\n                                                                setDragOverTarget(null);\n                                                            }\n                                                        },\n                                                        onDrop: (e)=>{\n                                                            if (draggedConversation) {\n                                                                handleConversationDrop(e, workspace.id);\n                                                            }\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleWorkspace(workspace.id),\n                                                                className: \"p-0.5 hover:bg-sidebar-accent rounded transition-colors\",\n                                                                children: expandedWorkspaces.has(workspace.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-sidebar-muted\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-sidebar-muted\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full flex-shrink-0\",\n                                                                style: {\n                                                                    backgroundColor: workspace.color\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-sidebar-foreground truncate\",\n                                                                children: workspace.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-sidebar-muted\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    ((_workspaceConversations_workspace_id = workspaceConversations[workspace.id]) === null || _workspaceConversations_workspace_id === void 0 ? void 0 : _workspaceConversations_workspace_id.length) || 0,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 803,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    expandedWorkspaces.has(workspace.id) && workspaceConversations[workspace.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 ml-6 mb-2\",\n                                                        children: workspaceConversations[workspace.id].map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"group relative p-2 rounded-lg cursor-pointer transition-colors\", currentConversationId === conversation.id ? \"bg-sidebar-accent\" : \"hover:bg-sidebar-accent/50\", draggedConversation === conversation.id && \"opacity-50\"),\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleConversationDragStart(e, conversation.id),\n                                                                onDragEnd: handleDragEnd,\n                                                                onClick: ()=>onSelectConversation(conversation.id),\n                                                                onMouseEnter: ()=>setHoveredId(conversation.id),\n                                                                onMouseLeave: ()=>setHoveredId(null),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: editingId === conversation.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: editTitle,\n                                                                                onChange: (e)=>setEditTitle(e.target.value),\n                                                                                onBlur: handleSaveEdit,\n                                                                                onKeyDown: (e)=>{\n                                                                                    if (e.key === 'Enter') handleSaveEdit();\n                                                                                    if (e.key === 'Escape') handleCancelEdit();\n                                                                                },\n                                                                                className: \"w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm\",\n                                                                                autoFocus: true,\n                                                                                onClick: (e)=>e.stopPropagation()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                lineNumber: 830,\n                                                                                columnNumber: 35\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium text-sm truncate\",\n                                                                                        children: conversation.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 845,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-sidebar-muted mt-1\",\n                                                                                        children: formatDate(conversation.updatedAt)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 848,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex gap-1 transition-opacity duration-200\", hoveredId === conversation.id ? \"opacity-100\" : \"opacity-0\"),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleStartEdit(conversation);\n                                                                                    },\n                                                                                    className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                    title: \"Edit conversation\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 868,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 860,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                setShowConversationMenu(showConversationMenu === conversation.id ? null : conversation.id);\n                                                                                            },\n                                                                                            className: \"p-1 hover:bg-sidebar-accent rounded-md transition-colors\",\n                                                                                            title: \"Move to workspace\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                                className: \"w-3 h-3\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 881,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 871,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        showConversationMenu === conversation.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"py-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                            handleAssignConversation(conversation.id, null);\n                                                                                                        },\n                                                                                                        className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors\",\n                                                                                                        children: \"\\uD83D\\uDCDD Unassigned\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                        lineNumber: 888,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    workspaces.map((ws)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                            onClick: (e)=>{\n                                                                                                                e.stopPropagation();\n                                                                                                                handleAssignConversation(conversation.id, ws.id);\n                                                                                                            },\n                                                                                                            className: \"w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2\",\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                    className: \"w-2 h-2 rounded-full flex-shrink-0\",\n                                                                                                                    style: {\n                                                                                                                        backgroundColor: ws.color\n                                                                                                                    }\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                                    lineNumber: 906,\n                                                                                                                    columnNumber: 45\n                                                                                                                }, this),\n                                                                                                                ws.name\n                                                                                                            ]\n                                                                                                        }, ws.id, true, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                            lineNumber: 898,\n                                                                                                            columnNumber: 43\n                                                                                                        }, this))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                                lineNumber: 887,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 886,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 870,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        onDeleteConversation(conversation.id);\n                                                                                    },\n                                                                                    className: \"p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors\",\n                                                                                    title: \"Delete conversation\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 925,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 917,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, conversation.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 809,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, workspace.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        draggedWorkspace && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"h-2 transition-all duration-200\", dragOverWorkspace === 'below-all' && \"bg-blue-500 rounded\"),\n                                            onDragOver: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                setDragOverWorkspace('below-all');\n                                            },\n                                            onDragLeave: ()=>setDragOverWorkspace(null),\n                                            onDrop: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleDropBetween(workspaces.length);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 p-3 border-t border-sidebar-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/files'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"File Manager\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 17\n                                            }, this),\n                                            settings.zepSettings.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/memory'),\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Memory Management\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onOpenSettings,\n                                                className: \"p-2 hover:bg-sidebar-accent rounded-lg transition-colors\",\n                                                title: \"Settings\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_FileText_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SyncStatus__WEBPACK_IMPORTED_MODULE_4__.SyncStatus, {\n                                        className: \"text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 990,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 961,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChatBubbleLeftIcon_ChevronDownIcon_ChevronRightIcon_EllipsisVerticalIcon_FolderPlusIcon_PencilIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 1001,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 997,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"fS+x1BkQ+GCvqupkBAwbiWVf/5M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0E7QUFjUDtBQUNvQjtBQUN4QjtBQUNTO0FBQ2U7QUFzQjFDLFNBQVNtQixRQUFRLEtBa0JqQjtRQWxCaUIsRUFDOUJDLGFBQWEsRUFDYkMsVUFBVSxFQUNWQyxxQkFBcUIsRUFDckJDLGtCQUFrQixFQUNsQkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsb0JBQW9CLEVBQ3BCQyxvQkFBb0IsRUFDcEJDLG9CQUFvQixFQUNwQkMsaUJBQWlCLEVBQ2pCQyxpQkFBaUIsRUFDakJDLGlCQUFpQixFQUNqQkMsaUJBQWlCLEVBQ2pCQyxtQkFBbUIsRUFDbkJDLCtCQUErQixFQUMvQkMsY0FBYyxFQUNELEdBbEJpQjs7SUFtQjlCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHdEMsK0NBQVFBLENBQWdCO0lBQzFELE1BQU0sQ0FBQ3VDLFdBQVdDLGFBQWEsR0FBR3hDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3lDLFdBQVdDLGFBQWEsR0FBRzFDLCtDQUFRQSxDQUFnQjtJQUMxRCxNQUFNLENBQUMyQyxvQkFBb0JDLHNCQUFzQixHQUFHNUMsK0NBQVFBLENBQWMsSUFBSTZDO0lBQzlFLE1BQU0sQ0FBQ0MsbUJBQW1CQyxxQkFBcUIsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ2dELG9CQUFvQkMsc0JBQXNCLEdBQUdqRCwrQ0FBUUEsQ0FBZ0I7SUFDNUUsTUFBTSxDQUFDa0Qsc0JBQXNCQyx3QkFBd0IsR0FBR25ELCtDQUFRQSxDQUFnQjtJQUNoRixNQUFNLENBQUNvRCxrQkFBa0JDLG9CQUFvQixHQUFHckQsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ3NELG1CQUFtQkMscUJBQXFCLEdBQUd2RCwrQ0FBUUEsQ0FBZ0I7SUFDMUUsTUFBTSxDQUFDd0Qsa0JBQWtCQyxvQkFBb0IsR0FBR3pELCtDQUFRQSxDQUFnQjtJQUN4RSxNQUFNLENBQUMwRCxxQkFBcUJDLHVCQUF1QixHQUFHM0QsK0NBQVFBLENBQWdCO0lBQzlFLE1BQU0sQ0FBQzRELGdCQUFnQkMsa0JBQWtCLEdBQUc3RCwrQ0FBUUEsQ0FBMkQ7SUFDL0csTUFBTSxDQUFDOEQsbUJBQW1CQyxxQkFBcUIsR0FBRy9ELCtDQUFRQSxDQUFDO1FBQ3pEZ0UsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE9BQU87SUFDVDtJQUNBLE1BQU1DLFNBQVNqRSwwREFBU0E7SUFDeEIsTUFBTSxFQUFFa0UsUUFBUSxFQUFFLEdBQUdsRCxzRUFBV0E7SUFFaEMsZ0RBQWdEO0lBQ2hEakIsZ0RBQVNBOzZCQUFDO1lBQ1IsTUFBTW9FO3dEQUFxQjtvQkFDekJsQix3QkFBd0I7Z0JBQzFCOztZQUVBLElBQUlELHNCQUFzQjtnQkFDeEJvQixTQUFTQyxnQkFBZ0IsQ0FBQyxTQUFTRjtnQkFDbkM7eUNBQU8sSUFBTUMsU0FBU0UsbUJBQW1CLENBQUMsU0FBU0g7O1lBQ3JEO1FBQ0Y7NEJBQUc7UUFBQ25CO0tBQXFCO0lBRXpCLE1BQU11QixrQkFBa0IsQ0FBQ0M7UUFDdkJwQyxhQUFhb0MsYUFBYUMsRUFBRTtRQUM1Qm5DLGFBQWFrQyxhQUFhRSxLQUFLO0lBQ2pDO0lBRUEsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUl4QyxhQUFhRSxVQUFVdUMsSUFBSSxJQUFJO1lBQ2pDakQscUJBQXFCUSxXQUFXRSxVQUFVdUMsSUFBSTtRQUNoRDtRQUNBeEMsYUFBYTtRQUNiRSxhQUFhO0lBQ2Y7SUFFQSxNQUFNdUMsbUJBQW1CO1FBQ3ZCekMsYUFBYTtRQUNiRSxhQUFhO0lBQ2Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTXdDLGtCQUFrQixDQUFDQztRQUN2QixNQUFNQyxjQUFjLElBQUlyQyxJQUFJRjtRQUM1QixJQUFJdUMsWUFBWUMsR0FBRyxDQUFDRixjQUFjO1lBQ2hDQyxZQUFZRSxNQUFNLENBQUNIO1FBQ3JCLE9BQU87WUFDTEMsWUFBWUcsR0FBRyxDQUFDSjtRQUNsQjtRQUNBckMsc0JBQXNCc0M7SUFDeEI7SUFFQSxNQUFNSSx3QkFBd0I7UUFDNUIsSUFBSXhCLGtCQUFrQkUsSUFBSSxDQUFDYyxJQUFJLElBQUk7WUFDakMvQyxrQkFBa0I7Z0JBQ2hCaUMsTUFBTUYsa0JBQWtCRSxJQUFJLENBQUNjLElBQUk7Z0JBQ2pDYixhQUFhSCxrQkFBa0JHLFdBQVcsQ0FBQ2EsSUFBSSxNQUFNUztnQkFDckRyQixPQUFPSixrQkFBa0JJLEtBQUs7WUFDaEM7WUFDQUgscUJBQXFCO2dCQUFFQyxNQUFNO2dCQUFJQyxhQUFhO2dCQUFJQyxPQUFPO1lBQVU7WUFDbkVuQixxQkFBcUI7UUFDdkI7SUFDRjtJQUVBLE1BQU15Qyx3QkFBd0I7UUFDNUIsSUFBSXhDLHNCQUFzQmMsa0JBQWtCRSxJQUFJLENBQUNjLElBQUksSUFBSTtZQUN2RCxNQUFNVyxZQUFZcEUsV0FBV3FFLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWhCLEVBQUUsS0FBSzNCO1lBQ2hELElBQUl5QyxXQUFXO2dCQUNiekQsa0JBQWtCO29CQUNoQixHQUFHeUQsU0FBUztvQkFDWnpCLE1BQU1GLGtCQUFrQkUsSUFBSSxDQUFDYyxJQUFJO29CQUNqQ2IsYUFBYUgsa0JBQWtCRyxXQUFXLENBQUNhLElBQUksTUFBTVM7b0JBQ3JEckIsT0FBT0osa0JBQWtCSSxLQUFLO2dCQUNoQztZQUNGO1lBQ0FqQixzQkFBc0I7WUFDdEJjLHFCQUFxQjtnQkFBRUMsTUFBTTtnQkFBSUMsYUFBYTtnQkFBSUMsT0FBTztZQUFVO1FBQ3JFO0lBQ0Y7SUFFQSxNQUFNMEIscUJBQXFCLENBQUNIO1FBQzFCeEMsc0JBQXNCd0MsVUFBVWQsRUFBRTtRQUNsQ1oscUJBQXFCO1lBQ25CQyxNQUFNeUIsVUFBVXpCLElBQUk7WUFDcEJDLGFBQWF3QixVQUFVeEIsV0FBVyxJQUFJO1lBQ3RDQyxPQUFPdUIsVUFBVXZCLEtBQUs7UUFDeEI7SUFDRjtJQUVBLE1BQU0yQix3QkFBd0IsQ0FBQ1o7UUFDN0IsSUFBSWEsUUFBUSwrRkFBK0Y7WUFDekc3RCxrQkFBa0JnRDtRQUNwQjtJQUNGO0lBRUEsTUFBTWMsc0NBQXNDLENBQUNkO1FBQzNDLDJEQUEyRDtRQUMzRHZELFVBQVV1RDtJQUNaO0lBRUEsTUFBTWUsMkJBQTJCLENBQUNDLGdCQUF3QmhCO1FBQ3hEOUMsZ0NBQWdDOEQsZ0JBQWdCaEI7UUFDaEQ5Qix3QkFBd0I7SUFDMUI7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTStDLGtCQUFrQixDQUFDQyxHQUFvQmxCO1FBQzNDNUIsb0JBQW9CNEI7UUFDcEJrQixFQUFFQyxZQUFZLENBQUNDLGFBQWEsR0FBRztRQUMvQkYsRUFBRUMsWUFBWSxDQUFDRSxPQUFPLENBQUMsY0FBY3JCO0lBQ3ZDO0lBRUEsTUFBTXNCLGlCQUFpQixDQUFDSixHQUFvQmxCO1FBQzFDa0IsRUFBRUssY0FBYztRQUNoQkwsRUFBRU0sZUFBZTtRQUNqQk4sRUFBRUMsWUFBWSxDQUFDTSxVQUFVLEdBQUc7UUFFNUIsSUFBSXRELG9CQUFvQkEscUJBQXFCNkIsYUFBYTtZQUN4RCx1RUFBdUU7WUFDdkUsTUFBTTBCLE9BQU8sRUFBR0MsYUFBYSxDQUFpQkMscUJBQXFCO1lBQ25FLE1BQU1DLE9BQU9ILEtBQUtJLEdBQUcsR0FBR0osS0FBS0ssTUFBTSxHQUFHO1lBQ3RDLE1BQU1DLGNBQWNkLEVBQUVlLE9BQU8sR0FBR0o7WUFFaEN2RCxxQkFBcUIwQjtZQUNyQixnREFBZ0Q7WUFDL0NrQixFQUFFUyxhQUFhLENBQWlCTyxPQUFPLENBQUNGLFdBQVcsR0FBR0EsWUFBWUcsUUFBUTtRQUM3RTtJQUNGO0lBRUEsTUFBTUMsa0JBQWtCLENBQUNsQjtRQUN2QkEsRUFBRUssY0FBYztRQUNoQmpELHFCQUFxQjtRQUNyQiw0QkFBNEI7UUFDM0I0QyxFQUFFUyxhQUFhLENBQWlCTyxPQUFPLENBQUNGLFdBQVcsR0FBRztJQUN6RDtJQUVBLE1BQU1LLGdCQUFnQjtRQUNwQmpFLG9CQUFvQjtRQUNwQkUscUJBQXFCO1FBQ3JCSSx1QkFBdUI7UUFDdkJFLGtCQUFrQjtJQUNwQjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNMEQsOEJBQThCLENBQUNwQixHQUFvQkY7UUFDdkR0Qyx1QkFBdUJzQztRQUN2QkUsRUFBRUMsWUFBWSxDQUFDQyxhQUFhLEdBQUc7UUFDL0JGLEVBQUVDLFlBQVksQ0FBQ0UsT0FBTyxDQUFDLGNBQWNMO0lBQ3ZDO0lBRUEsTUFBTXVCLDBCQUEwQixDQUFDckIsR0FBb0JsQjtRQUNuRGtCLEVBQUVLLGNBQWM7UUFDaEJMLEVBQUVNLGVBQWU7UUFFakIsSUFBSS9DLHFCQUFxQjtZQUN2Qkcsa0JBQWtCO2dCQUFFNEQsTUFBTXhDLGNBQWMsY0FBYztnQkFBY04sSUFBSU07WUFBWTtRQUN0RjtJQUNGO0lBRUEsTUFBTXlDLHlCQUF5QixPQUFPdkIsR0FBb0J3QjtRQUN4RHhCLEVBQUVLLGNBQWM7UUFDaEJMLEVBQUVNLGVBQWU7UUFFakIsSUFBSSxDQUFDL0MscUJBQXFCO1FBRTFCLElBQUk7WUFDRixNQUFNdkIsZ0NBQWdDdUIscUJBQXFCaUUscUJBQXFCO1FBQ2xGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUNoRCxTQUFVO1lBQ1JqRSx1QkFBdUI7WUFDdkJFLGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsTUFBTWlFLG9CQUFvQixPQUFPQztRQUMvQixJQUFJLENBQUMzRSxrQkFBa0I7WUFDckJDLG9CQUFvQjtZQUNwQkUscUJBQXFCO1lBQ3JCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsMENBQTBDO1lBQzFDLE1BQU15RSxlQUFlM0csV0FBVzRHLFNBQVMsQ0FBQ3RDLENBQUFBLElBQUtBLEVBQUVoQixFQUFFLEtBQUt2QjtZQUV4RCxJQUFJNEUsaUJBQWlCLENBQUMsR0FBRztZQUV6QkgsUUFBUUssR0FBRyxDQUFDLHFDQUFxQztnQkFDL0M5RTtnQkFDQTRFO2dCQUNBRDtnQkFDQUksaUJBQWlCOUcsV0FBVytHLE1BQU07Z0JBQ2xDQyxjQUFjaEgsV0FBV2lILEdBQUcsQ0FBQzNDLENBQUFBLElBQUtBLEVBQUVoQixFQUFFO1lBQ3hDO1lBRUEsdUNBQXVDO1lBQ3ZDLElBQUk0RCxnQkFBZ0JSO1lBQ3BCLElBQUlDLGVBQWVELGFBQWE7Z0JBQzlCUSxnQkFBZ0JSLGNBQWM7WUFDaEM7WUFFQUYsUUFBUUssR0FBRyxDQUFDLDRCQUE0Qks7WUFFeEMsNkNBQTZDO1lBQzdDLElBQUlQLGlCQUFpQk8sZUFBZTtnQkFDbENWLFFBQVFLLEdBQUcsQ0FBQztnQkFDWjtZQUNGO1lBRUEsbUJBQW1CO1lBQ25CLE1BQU1NLGdCQUFnQjttQkFBSW5IO2FBQVc7WUFDckMsTUFBTSxDQUFDb0gsWUFBWSxHQUFHRCxjQUFjRSxNQUFNLENBQUNWLGNBQWM7WUFDekRRLGNBQWNFLE1BQU0sQ0FBQ0gsZUFBZSxHQUFHRTtZQUV2Qyx3Q0FBd0M7WUFDeEMsTUFBTUosZUFBZUcsY0FBY0YsR0FBRyxDQUFDM0MsQ0FBQUEsSUFBS0EsRUFBRWhCLEVBQUU7WUFFaERrRCxRQUFRSyxHQUFHLENBQUMsNENBQTRDRztZQUN4RCxNQUFNbkcsb0JBQW9CbUc7UUFDNUIsRUFBRSxPQUFPVCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pELFNBQVU7WUFDUnZFLG9CQUFvQjtZQUNwQkUscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTW9GLDBCQUEwQnZILGNBQWN3SCxNQUFNLENBQUNDLENBQUFBLE9BQVEsQ0FBQ0EsS0FBSzVELFdBQVc7SUFDOUUsTUFBTTZELHlCQUF5QnpILFdBQVcwSCxNQUFNLENBQUMsQ0FBQ0MsS0FBS3ZEO1FBQ3JEdUQsR0FBRyxDQUFDdkQsVUFBVWQsRUFBRSxDQUFDLEdBQUd2RCxjQUFjd0gsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLNUQsV0FBVyxLQUFLUSxVQUFVZCxFQUFFO1FBQ2xGLE9BQU9xRTtJQUNULEdBQUcsQ0FBQztJQUtKLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEIsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRjtRQUN0QixNQUFNRyxNQUFNLElBQUlEO1FBQ2hCLE1BQU1FLGFBQWFDLEtBQUtDLEtBQUssQ0FBQyxDQUFDSCxJQUFJSSxPQUFPLEtBQUtOLEtBQUtNLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxLQUFLLEVBQUM7UUFFcEYsSUFBSUgsZUFBZSxHQUFHO1lBQ3BCLE9BQU87UUFDVCxPQUFPLElBQUlBLGVBQWUsR0FBRztZQUMzQixPQUFPO1FBQ1QsT0FBTyxJQUFJQSxhQUFhLEdBQUc7WUFDekIsT0FBTyxHQUFjLE9BQVhBLFlBQVc7UUFDdkIsT0FBTztZQUNMLE9BQU9ILEtBQUtPLGtCQUFrQjtRQUNoQztJQUNGO0lBRUEscUJBQ0U7O1lBRUdsSSx3QkFDQyw4REFBQ21JO2dCQUNDQyxXQUFVO2dCQUNWQyxTQUFTcEk7Ozs7OzswQkFLYiw4REFBQ2tJO2dCQUFJQyxXQUFXNUksOENBQUVBLENBQ2hCLG1IQUNBLGdCQUNBUSxTQUFTLGtCQUFrQixxQkFDM0I7MEJBRUEsNEVBQUNtSTtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQUdGLFdBQVU7OENBQW9COzs7Ozs7OENBQ2xDLDhEQUFDRztvQ0FDQ0YsU0FBU3BJO29DQUNUbUksV0FBVTs4Q0FFViw0RUFBQ3JKLG1PQUFTQTt3Q0FBQ3FKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUt6Qiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNHO2dDQUNDRixTQUFTbkk7Z0NBQ1RrSSxXQUFVOztrREFFViw4REFBQ3pKLG1PQUFRQTt3Q0FBQ3lKLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7O3NDQU1wQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0k7b0RBQUdKLFdBQVU7OERBQXlDOzs7Ozs7OERBQ3ZELDhEQUFDRztvREFDQ0YsU0FBUyxJQUFNOUcscUJBQXFCO29EQUNwQzZHLFdBQVU7b0RBQ1ZoRixPQUFNOzhEQUVOLDRFQUFDakUsbU9BQWNBO3dEQUFDaUosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBSzdCOUcsbUNBQ0MsOERBQUM2Rzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNLO29EQUNDeEMsTUFBSztvREFDTHlDLGFBQVk7b0RBQ1pDLE9BQU9yRyxrQkFBa0JFLElBQUk7b0RBQzdCb0csVUFBVSxDQUFDakUsSUFBTXBDLHFCQUFxQnNHLENBQUFBLE9BQVM7Z0VBQUUsR0FBR0EsSUFBSTtnRUFBRXJHLE1BQU1tQyxFQUFFbUUsTUFBTSxDQUFDSCxLQUFLOzREQUFDO29EQUMvRVAsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDSztvREFDQ3hDLE1BQUs7b0RBQ0x5QyxhQUFZO29EQUNaQyxPQUFPckcsa0JBQWtCRyxXQUFXO29EQUNwQ21HLFVBQVUsQ0FBQ2pFLElBQU1wQyxxQkFBcUJzRyxDQUFBQSxPQUFTO2dFQUFFLEdBQUdBLElBQUk7Z0VBQUVwRyxhQUFha0MsRUFBRW1FLE1BQU0sQ0FBQ0gsS0FBSzs0REFBQztvREFDdEZQLFdBQVU7Ozs7Ozs4REFFWiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDSzs0REFDQ3hDLE1BQUs7NERBQ0wwQyxPQUFPckcsa0JBQWtCSSxLQUFLOzREQUM5QmtHLFVBQVUsQ0FBQ2pFLElBQU1wQyxxQkFBcUJzRyxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUVuRyxPQUFPaUMsRUFBRW1FLE1BQU0sQ0FBQ0gsS0FBSztvRUFBQzs0REFDaEZQLFdBQVU7Ozs7OztzRUFFWiw4REFBQ1c7NERBQUtYLFdBQVU7c0VBQTZCOzs7Ozs7Ozs7Ozs7OERBRS9DLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNHOzREQUNDRixTQUFTdkU7NERBQ1RzRSxXQUFVO3NFQUNYOzs7Ozs7c0VBR0QsOERBQUNHOzREQUNDRixTQUFTO2dFQUNQOUcscUJBQXFCO2dFQUNyQmdCLHFCQUFxQjtvRUFBRUMsTUFBTTtvRUFBSUMsYUFBYTtvRUFBSUMsT0FBTztnRUFBVTs0REFDckU7NERBQ0EwRixXQUFVO3NFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBUU41RyxvQ0FDQyw4REFBQzJHOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0s7b0RBQ0N4QyxNQUFLO29EQUNMeUMsYUFBWTtvREFDWkMsT0FBT3JHLGtCQUFrQkUsSUFBSTtvREFDN0JvRyxVQUFVLENBQUNqRSxJQUFNcEMscUJBQXFCc0csQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFckcsTUFBTW1DLEVBQUVtRSxNQUFNLENBQUNILEtBQUs7NERBQUM7b0RBQy9FUCxXQUFVOzs7Ozs7OERBRVosOERBQUNLO29EQUNDeEMsTUFBSztvREFDTHlDLGFBQVk7b0RBQ1pDLE9BQU9yRyxrQkFBa0JHLFdBQVc7b0RBQ3BDbUcsVUFBVSxDQUFDakUsSUFBTXBDLHFCQUFxQnNHLENBQUFBLE9BQVM7Z0VBQUUsR0FBR0EsSUFBSTtnRUFBRXBHLGFBQWFrQyxFQUFFbUUsTUFBTSxDQUFDSCxLQUFLOzREQUFDO29EQUN0RlAsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNLOzREQUNDeEMsTUFBSzs0REFDTDBDLE9BQU9yRyxrQkFBa0JJLEtBQUs7NERBQzlCa0csVUFBVSxDQUFDakUsSUFBTXBDLHFCQUFxQnNHLENBQUFBLE9BQVM7d0VBQUUsR0FBR0EsSUFBSTt3RUFBRW5HLE9BQU9pQyxFQUFFbUUsTUFBTSxDQUFDSCxLQUFLO29FQUFDOzREQUNoRlAsV0FBVTs7Ozs7O3NFQUVaLDhEQUFDVzs0REFBS1gsV0FBVTtzRUFBNkI7Ozs7Ozs7Ozs7Ozs4REFFL0MsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0c7NERBQ0NGLFNBQVNyRTs0REFDVG9FLFdBQVU7c0VBQ1g7Ozs7OztzRUFHRCw4REFBQ0c7NERBQ0NGLFNBQVM7Z0VBQ1A1RyxzQkFBc0I7Z0VBQ3RCYyxxQkFBcUI7b0VBQUVDLE1BQU07b0VBQUlDLGFBQWE7b0VBQUlDLE9BQU87Z0VBQVU7NERBQ3JFOzREQUNBMEYsV0FBVTtzRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVNSdkksV0FBVytHLE1BQU0sR0FBRyxtQkFDbkIsOERBQUN1QjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFHSixXQUFVO3NEQUFtRDs7Ozs7O3NEQUNqRSw4REFBQ0Q7NENBQUlDLFdBQVU7O2dEQUVaeEcsa0NBQ0MsOERBQUN1RztvREFDQ0MsV0FBVzVJLDhDQUFFQSxDQUNYLHlDQUNBc0Msc0JBQXNCLDJCQUEyQjtvREFFbkRrSCxZQUFZLENBQUNyRTt3REFDWEEsRUFBRUssY0FBYzt3REFDaEJMLEVBQUVNLGVBQWU7d0RBQ2pCbEQscUJBQXFCO29EQUN2QjtvREFDQWtILGFBQWEsSUFBTWxILHFCQUFxQjtvREFDeENtSCxRQUFRLENBQUN2RTt3REFDUEEsRUFBRUssY0FBYzt3REFDaEJMLEVBQUVNLGVBQWU7d0RBQ2pCcUIsa0JBQWtCLElBQUksMEJBQTBCO29EQUNsRDs7Ozs7O2dEQUtIekcsV0FBV2lILEdBQUcsQ0FBQyxDQUFDN0MsV0FBV2tGO3dEQTJDbEI3Qjt5RUExQ1IsOERBQUNhOzs0REFFRXZHLG9CQUFvQkEscUJBQXFCcUMsVUFBVWQsRUFBRSxJQUFJZ0csUUFBUSxtQkFDaEUsOERBQUNoQjtnRUFDQ0MsV0FBVzVJLDhDQUFFQSxDQUNYLHlDQUNBc0Msc0JBQXNCLFNBQXNCLE9BQWJtQyxVQUFVZCxFQUFFLEtBQU07Z0VBRW5ENkYsWUFBWSxDQUFDckU7b0VBQ1hBLEVBQUVLLGNBQWM7b0VBQ2hCTCxFQUFFTSxlQUFlO29FQUNqQmxELHFCQUFxQixTQUFzQixPQUFia0MsVUFBVWQsRUFBRTtnRUFDNUM7Z0VBQ0E4RixhQUFhLElBQU1sSCxxQkFBcUI7Z0VBQ3hDbUgsUUFBUSxDQUFDdkU7b0VBQ1BBLEVBQUVLLGNBQWM7b0VBQ2hCTCxFQUFFTSxlQUFlO29FQUNqQnFCLGtCQUFrQjZDO2dFQUNwQjs7Ozs7OzBFQUlKLDhEQUFDaEI7Z0VBQ0NDLFdBQVc1SSw4Q0FBRUEsQ0FDWCxpR0FDQW9DLHFCQUFxQnFDLFVBQVVkLEVBQUUsSUFBSSx1QkFDckN2QixvQkFBb0JBLHFCQUFxQnFDLFVBQVVkLEVBQUUsSUFBSTtnRUFFM0RpRyxXQUFXO2dFQUNYQyxhQUFhLENBQUMxRSxJQUFNRCxnQkFBZ0JDLEdBQUdWLFVBQVVkLEVBQUU7Z0VBQ25EbUcsV0FBV3hEO2dFQUNYeUQsY0FBYyxJQUFNdEgsb0JBQW9CZ0MsVUFBVWQsRUFBRTtnRUFDcERxRyxjQUFjLElBQU12SCxvQkFBb0I7O2tGQUV4Qyw4REFBQ2tHO3dFQUNDQyxXQUFVO3dFQUNWcUIsT0FBTzs0RUFBRUMsaUJBQWlCekYsVUFBVXZCLEtBQUs7d0VBQUM7Ozs7OztrRkFFNUMsOERBQUNxRzt3RUFBS1gsV0FBVTtrRkFDYm5FLFVBQVV6QixJQUFJOzs7Ozs7a0ZBRWpCLDhEQUFDdUc7d0VBQUtYLFdBQVU7OzRFQUE2Qjs0RUFDekNkLEVBQUFBLHVDQUFBQSxzQkFBc0IsQ0FBQ3JELFVBQVVkLEVBQUUsQ0FBQyxjQUFwQ21FLDJEQUFBQSxxQ0FBc0NWLE1BQU0sS0FBSTs0RUFBRTs7Ozs7OztrRkFFdEQsOERBQUN1Qjt3RUFBSUMsV0FBVTt3RUFBcUJnQixXQUFXOzswRkFDN0MsOERBQUNiO2dGQUNDRixTQUFTLElBQU05RCxvQ0FBb0NOLFVBQVVkLEVBQUU7Z0ZBQy9EaUYsV0FBVzVJLDhDQUFFQSxDQUNYLHdHQUNBd0MscUJBQXFCaUMsVUFBVWQsRUFBRSxHQUFHLGdCQUFnQjtnRkFFdERDLE9BQU07Z0ZBQ05nRyxXQUFXO2dGQUNYTyxhQUFhLENBQUNoRixJQUFNQSxFQUFFTSxlQUFlOzBGQUVyQyw0RUFBQ3RHLG1PQUFRQTtvRkFBQ3lKLFdBQVU7Ozs7Ozs7Ozs7OzBGQUV0Qiw4REFBQ0c7Z0ZBQ0NGLFNBQVMsSUFBTWpFLG1CQUFtQkg7Z0ZBQ2xDbUUsV0FBVzVJLDhDQUFFQSxDQUNYLHNFQUNBd0MscUJBQXFCaUMsVUFBVWQsRUFBRSxHQUFHLGdCQUFnQjtnRkFFdERDLE9BQU07Z0ZBQ05nRyxXQUFXO2dGQUNYTyxhQUFhLENBQUNoRixJQUFNQSxFQUFFTSxlQUFlOzBGQUVyQyw0RUFBQ25HLG1PQUFVQTtvRkFBQ3NKLFdBQVU7Ozs7Ozs7Ozs7OzBGQUV4Qiw4REFBQ0c7Z0ZBQ0NGLFNBQVMsSUFBTWhFLHNCQUFzQkosVUFBVWQsRUFBRTtnRkFDakRpRixXQUFXNUksOENBQUVBLENBQ1gsa0dBQ0F3QyxxQkFBcUJpQyxVQUFVZCxFQUFFLEdBQUcsZ0JBQWdCO2dGQUV0REMsT0FBTTtnRkFDTmdHLFdBQVc7Z0ZBQ1hPLGFBQWEsQ0FBQ2hGLElBQU1BLEVBQUVNLGVBQWU7MEZBRXJDLDRFQUFDcEcsb09BQVNBO29GQUFDdUosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VEQS9FbkJuRSxVQUFVZCxFQUFFOzs7Ozs7Z0RBdUZ2QnZCLGtDQUNDLDhEQUFDdUc7b0RBQ0NDLFdBQVc1SSw4Q0FBRUEsQ0FDWCxtQ0FDQXNDLHNCQUFzQiwwQkFBMEI7b0RBRWxEa0gsWUFBWSxDQUFDckU7d0RBQ1hBLEVBQUVLLGNBQWM7d0RBQ2hCTCxFQUFFTSxlQUFlO3dEQUNqQmxELHFCQUFxQjtvREFDdkI7b0RBQ0FrSCxhQUFhLElBQU1sSCxxQkFBcUI7b0RBQ3hDbUgsUUFBUSxDQUFDdkU7d0RBQ1BBLEVBQUVLLGNBQWM7d0RBQ2hCTCxFQUFFTSxlQUFlO3dEQUNqQnFCLGtCQUFrQnpHLFdBQVcrRyxNQUFNO29EQUNyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVFUaEgsY0FBY2dILE1BQU0sS0FBSyxrQkFDeEIsOERBQUN1QjtvQ0FBSUMsV0FBVTs4Q0FBc0M7Ozs7O3lEQUlyRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDSTs0Q0FBR0osV0FBVTtzREFBbUQ7Ozs7Ozt3Q0FHaEVqQix3QkFBd0JQLE1BQU0sR0FBRyxtQkFDaEMsOERBQUN1Qjs7OERBQ0MsOERBQUNBO29EQUNDQyxXQUFXNUksOENBQUVBLENBQ1gsaUVBQ0E0QyxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCNkQsSUFBSSxNQUFLLGdCQUFnQjtvREFFM0MrQyxZQUFZLENBQUNyRSxJQUFNekMsdUJBQXVCOEQsd0JBQXdCckI7b0RBQ2xFdUUsUUFBUSxDQUFDdkUsSUFBTXpDLHVCQUF1QmdFLHVCQUF1QnZCOztzRUFFN0QsOERBQUMvRixvT0FBa0JBOzREQUFDd0osV0FBVTs7Ozs7O3NFQUM5Qiw4REFBQ1c7NERBQUtYLFdBQVU7c0VBQXlDOzs7Ozs7c0VBQ3pELDhEQUFDVzs0REFBS1gsV0FBVTs7Z0VBQTZCO2dFQUFFakIsd0JBQXdCUCxNQUFNO2dFQUFDOzs7Ozs7Ozs7Ozs7OzhEQUVoRiw4REFBQ3VCO29EQUFJQyxXQUFVOzhEQUNaakIsd0JBQXdCTCxHQUFHLENBQUMsQ0FBQzVELDZCQUM1Qiw4REFBQ2lGOzREQUVDQyxXQUFXNUksOENBQUVBLENBQ1gsa0VBQ0FNLDBCQUEwQm9ELGFBQWFDLEVBQUUsR0FDckMsc0JBQ0EsOEJBQ0pqQix3QkFBd0JnQixhQUFhQyxFQUFFLElBQUk7NERBRTdDaUcsU0FBUzs0REFDVEMsYUFBYSxDQUFDMUUsSUFBTW9CLDRCQUE0QnBCLEdBQUd6QixhQUFhQyxFQUFFOzREQUNsRW1HLFdBQVd4RDs0REFDWHVDLFNBQVMsSUFBTWxJLHFCQUFxQitDLGFBQWFDLEVBQUU7NERBQ25Eb0csY0FBYyxJQUFNckksYUFBYWdDLGFBQWFDLEVBQUU7NERBQ2hEcUcsY0FBYyxJQUFNdEksYUFBYTtzRUFFakMsNEVBQUNpSDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNadkgsY0FBY3FDLGFBQWFDLEVBQUUsaUJBQzVCLDhEQUFDc0Y7NEVBQ0N4QyxNQUFLOzRFQUNMMEMsT0FBTzVIOzRFQUNQNkgsVUFBVSxDQUFDakUsSUFBTTNELGFBQWEyRCxFQUFFbUUsTUFBTSxDQUFDSCxLQUFLOzRFQUM1Q2lCLFFBQVF2Rzs0RUFDUndHLFdBQVcsQ0FBQ2xGO2dGQUNWLElBQUlBLEVBQUVtRixHQUFHLEtBQUssU0FBU3pHO2dGQUN2QixJQUFJc0IsRUFBRW1GLEdBQUcsS0FBSyxVQUFVdkc7NEVBQzFCOzRFQUNBNkUsV0FBVTs0RUFDVjJCLFNBQVM7NEVBQ1QxQixTQUFTLENBQUMxRCxJQUFNQSxFQUFFTSxlQUFlOzs7OztpR0FHbkM7OzhGQUNFLDhEQUFDa0Q7b0ZBQUlDLFdBQVU7OEZBQ1psRixhQUFhRSxLQUFLOzs7Ozs7OEZBRXJCLDhEQUFDK0U7b0ZBQUlDLFdBQVU7OEZBQ1pYLFdBQVd2RSxhQUFhOEcsU0FBUzs7Ozs7Ozs7Ozs7OztrRkFPMUMsOERBQUM3Qjt3RUFBSUMsV0FBVzVJLDhDQUFFQSxDQUNoQiw4Q0FDQXlCLGNBQWNpQyxhQUFhQyxFQUFFLEdBQUcsZ0JBQWdCOzswRkFFaEQsOERBQUNvRjtnRkFDQ0YsU0FBUyxDQUFDMUQ7b0ZBQ1JBLEVBQUVNLGVBQWU7b0ZBQ2pCaEMsZ0JBQWdCQztnRkFDbEI7Z0ZBQ0FrRixXQUFVO2dGQUNWaEYsT0FBTTswRkFFTiw0RUFBQ3RFLG1PQUFVQTtvRkFBQ3NKLFdBQVU7Ozs7Ozs7Ozs7OzBGQUV4Qiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRzt3RkFDQ0YsU0FBUyxDQUFDMUQ7NEZBQ1JBLEVBQUVNLGVBQWU7NEZBQ2pCdEQsd0JBQ0VELHlCQUF5QndCLGFBQWFDLEVBQUUsR0FBRyxPQUFPRCxhQUFhQyxFQUFFO3dGQUVyRTt3RkFDQWlGLFdBQVU7d0ZBQ1ZoRixPQUFNO2tHQUVOLDRFQUFDaEUsb09BQW9CQTs0RkFBQ2dKLFdBQVU7Ozs7Ozs7Ozs7O29GQUlqQzFHLHlCQUF5QndCLGFBQWFDLEVBQUUsa0JBQ3ZDLDhEQUFDZ0Y7d0ZBQUlDLFdBQVU7a0dBQ2IsNEVBQUNEOzRGQUFJQyxXQUFVOzs4R0FDYiw4REFBQ0c7b0dBQ0NGLFNBQVMsQ0FBQzFEO3dHQUNSQSxFQUFFTSxlQUFlO3dHQUNqQlQseUJBQXlCdEIsYUFBYUMsRUFBRSxFQUFFO29HQUM1QztvR0FDQWlGLFdBQVU7OEdBQ1g7Ozs7OztnR0FHQXZJLFdBQVdpSCxHQUFHLENBQUMsQ0FBQ21ELG1CQUNmLDhEQUFDMUI7d0dBRUNGLFNBQVMsQ0FBQzFEOzRHQUNSQSxFQUFFTSxlQUFlOzRHQUNqQlQseUJBQXlCdEIsYUFBYUMsRUFBRSxFQUFFOEcsR0FBRzlHLEVBQUU7d0dBQ2pEO3dHQUNBaUYsV0FBVTs7MEhBRVYsOERBQUNEO2dIQUNDQyxXQUFVO2dIQUNWcUIsT0FBTztvSEFBRUMsaUJBQWlCTyxHQUFHdkgsS0FBSztnSEFBQzs7Ozs7OzRHQUVwQ3VILEdBQUd6SCxJQUFJOzt1R0FYSHlILEdBQUc5RyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQWtCdEIsOERBQUNvRjtnRkFDQ0YsU0FBUyxDQUFDMUQ7b0ZBQ1JBLEVBQUVNLGVBQWU7b0ZBQ2pCN0UscUJBQXFCOEMsYUFBYUMsRUFBRTtnRkFDdEM7Z0ZBQ0FpRixXQUFVO2dGQUNWaEYsT0FBTTswRkFFTiw0RUFBQ3ZFLG9PQUFTQTtvRkFBQ3VKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJEQWpIdEJsRixhQUFhQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dDQTRIN0J0RCxXQUFXaUgsR0FBRyxDQUFDLENBQUM3QztnREF5Q1BxRDtpRUF4Q1IsOERBQUNhOztrRUFDQyw4REFBQ0E7d0RBQ0NDLFdBQVc1SSw4Q0FBRUEsQ0FDWCxpRUFDQTRDLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0I2RCxJQUFJLE1BQUssZUFBZTdELGVBQWVlLEVBQUUsS0FBS2MsVUFBVWQsRUFBRSxJQUFJO3dEQUVoRjZGLFlBQVksQ0FBQ3JFOzREQUNYLElBQUl6QyxxQkFBcUI7Z0VBQ3ZCOEQsd0JBQXdCckIsR0FBR1YsVUFBVWQsRUFBRTs0REFDekM7d0RBQ0Y7d0RBQ0E4RixhQUFhLENBQUN0RTs0REFDWixJQUFJekMscUJBQXFCO2dFQUN2Qkcsa0JBQWtCOzREQUNwQjt3REFDRjt3REFDQTZHLFFBQVEsQ0FBQ3ZFOzREQUNQLElBQUl6QyxxQkFBcUI7Z0VBQ3ZCZ0UsdUJBQXVCdkIsR0FBR1YsVUFBVWQsRUFBRTs0REFDeEM7d0RBQ0Y7OzBFQUVBLDhEQUFDb0Y7Z0VBQ0NGLFNBQVMsSUFBTTdFLGdCQUFnQlMsVUFBVWQsRUFBRTtnRUFDM0NpRixXQUFVOzBFQUVUakgsbUJBQW1Cd0MsR0FBRyxDQUFDTSxVQUFVZCxFQUFFLGtCQUNsQyw4REFBQ2xFLG9PQUFlQTtvRUFBQ21KLFdBQVU7Ozs7O3lGQUUzQiw4REFBQ2xKLG9PQUFnQkE7b0VBQUNrSixXQUFVOzs7Ozs7Ozs7OzswRUFHaEMsOERBQUNEO2dFQUNDQyxXQUFVO2dFQUNWcUIsT0FBTztvRUFBRUMsaUJBQWlCekYsVUFBVXZCLEtBQUs7Z0VBQUM7Ozs7OzswRUFFNUMsOERBQUNxRztnRUFBS1gsV0FBVTswRUFDYm5FLFVBQVV6QixJQUFJOzs7Ozs7MEVBRWpCLDhEQUFDdUc7Z0VBQUtYLFdBQVU7O29FQUE2QjtvRUFDekNkLEVBQUFBLHVDQUFBQSxzQkFBc0IsQ0FBQ3JELFVBQVVkLEVBQUUsQ0FBQyxjQUFwQ21FLDJEQUFBQSxxQ0FBc0NWLE1BQU0sS0FBSTtvRUFBRTs7Ozs7Ozs7Ozs7OztvREFJdkR6RixtQkFBbUJ3QyxHQUFHLENBQUNNLFVBQVVkLEVBQUUsS0FBS21FLHNCQUFzQixDQUFDckQsVUFBVWQsRUFBRSxDQUFDLGtCQUMzRSw4REFBQ2dGO3dEQUFJQyxXQUFVO2tFQUNaZCxzQkFBc0IsQ0FBQ3JELFVBQVVkLEVBQUUsQ0FBQyxDQUFDMkQsR0FBRyxDQUFDLENBQUM1RCw2QkFDekMsOERBQUNpRjtnRUFFQ0MsV0FBVzVJLDhDQUFFQSxDQUNYLGtFQUNBTSwwQkFBMEJvRCxhQUFhQyxFQUFFLEdBQ3JDLHNCQUNBLDhCQUNKakIsd0JBQXdCZ0IsYUFBYUMsRUFBRSxJQUFJO2dFQUU3Q2lHLFNBQVM7Z0VBQ1RDLGFBQWEsQ0FBQzFFLElBQU1vQiw0QkFBNEJwQixHQUFHekIsYUFBYUMsRUFBRTtnRUFDbEVtRyxXQUFXeEQ7Z0VBQ1h1QyxTQUFTLElBQU1sSSxxQkFBcUIrQyxhQUFhQyxFQUFFO2dFQUNuRG9HLGNBQWMsSUFBTXJJLGFBQWFnQyxhQUFhQyxFQUFFO2dFQUNoRHFHLGNBQWMsSUFBTXRJLGFBQWE7MEVBRWpDLDRFQUFDaUg7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDWnZILGNBQWNxQyxhQUFhQyxFQUFFLGlCQUM1Qiw4REFBQ3NGO2dGQUNDeEMsTUFBSztnRkFDTDBDLE9BQU81SDtnRkFDUDZILFVBQVUsQ0FBQ2pFLElBQU0zRCxhQUFhMkQsRUFBRW1FLE1BQU0sQ0FBQ0gsS0FBSztnRkFDNUNpQixRQUFRdkc7Z0ZBQ1J3RyxXQUFXLENBQUNsRjtvRkFDVixJQUFJQSxFQUFFbUYsR0FBRyxLQUFLLFNBQVN6RztvRkFDdkIsSUFBSXNCLEVBQUVtRixHQUFHLEtBQUssVUFBVXZHO2dGQUMxQjtnRkFDQTZFLFdBQVU7Z0ZBQ1YyQixTQUFTO2dGQUNUMUIsU0FBUyxDQUFDMUQsSUFBTUEsRUFBRU0sZUFBZTs7Ozs7cUdBR25DOztrR0FDRSw4REFBQ2tEO3dGQUFJQyxXQUFVO2tHQUNabEYsYUFBYUUsS0FBSzs7Ozs7O2tHQUVyQiw4REFBQytFO3dGQUFJQyxXQUFVO2tHQUNaWCxXQUFXdkUsYUFBYThHLFNBQVM7Ozs7Ozs7Ozs7Ozs7c0ZBTzFDLDhEQUFDN0I7NEVBQUlDLFdBQVc1SSw4Q0FBRUEsQ0FDaEIsOENBQ0F5QixjQUFjaUMsYUFBYUMsRUFBRSxHQUFHLGdCQUFnQjs7OEZBRWhELDhEQUFDb0Y7b0ZBQ0NGLFNBQVMsQ0FBQzFEO3dGQUNSQSxFQUFFTSxlQUFlO3dGQUNqQmhDLGdCQUFnQkM7b0ZBQ2xCO29GQUNBa0YsV0FBVTtvRkFDVmhGLE9BQU07OEZBRU4sNEVBQUN0RSxtT0FBVUE7d0ZBQUNzSixXQUFVOzs7Ozs7Ozs7Ozs4RkFFeEIsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ0c7NEZBQ0NGLFNBQVMsQ0FBQzFEO2dHQUNSQSxFQUFFTSxlQUFlO2dHQUNqQnRELHdCQUNFRCx5QkFBeUJ3QixhQUFhQyxFQUFFLEdBQUcsT0FBT0QsYUFBYUMsRUFBRTs0RkFFckU7NEZBQ0FpRixXQUFVOzRGQUNWaEYsT0FBTTtzR0FFTiw0RUFBQ2hFLG9PQUFvQkE7Z0dBQUNnSixXQUFVOzs7Ozs7Ozs7Ozt3RkFJakMxRyx5QkFBeUJ3QixhQUFhQyxFQUFFLGtCQUN2Qyw4REFBQ2dGOzRGQUFJQyxXQUFVO3NHQUNiLDRFQUFDRDtnR0FBSUMsV0FBVTs7a0hBQ2IsOERBQUNHO3dHQUNDRixTQUFTLENBQUMxRDs0R0FDUkEsRUFBRU0sZUFBZTs0R0FDakJULHlCQUF5QnRCLGFBQWFDLEVBQUUsRUFBRTt3R0FDNUM7d0dBQ0FpRixXQUFVO2tIQUNYOzs7Ozs7b0dBR0F2SSxXQUFXaUgsR0FBRyxDQUFDLENBQUNtRCxtQkFDZiw4REFBQzFCOzRHQUVDRixTQUFTLENBQUMxRDtnSEFDUkEsRUFBRU0sZUFBZTtnSEFDakJULHlCQUF5QnRCLGFBQWFDLEVBQUUsRUFBRThHLEdBQUc5RyxFQUFFOzRHQUNqRDs0R0FDQWlGLFdBQVU7OzhIQUVWLDhEQUFDRDtvSEFDQ0MsV0FBVTtvSEFDVnFCLE9BQU87d0hBQUVDLGlCQUFpQk8sR0FBR3ZILEtBQUs7b0hBQUM7Ozs7OztnSEFFcEN1SCxHQUFHekgsSUFBSTs7MkdBWEh5SCxHQUFHOUcsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RkFrQnRCLDhEQUFDb0Y7b0ZBQ0NGLFNBQVMsQ0FBQzFEO3dGQUNSQSxFQUFFTSxlQUFlO3dGQUNqQjdFLHFCQUFxQjhDLGFBQWFDLEVBQUU7b0ZBQ3RDO29GQUNBaUYsV0FBVTtvRkFDVmhGLE9BQU07OEZBRU4sNEVBQUN2RSxvT0FBU0E7d0ZBQUN1SixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrREFqSHRCbEYsYUFBYUMsRUFBRTs7Ozs7Ozs7Ozs7K0NBaERwQmMsVUFBVWQsRUFBRTs7Ozs7O3dDQTZLdkJ2QixrQ0FDQyw4REFBQ3VHOzRDQUNDQyxXQUFXNUksOENBQUVBLENBQ1gsbUNBQ0FzQyxzQkFBc0IsZUFBZTs0Q0FFdkNrSCxZQUFZLENBQUNyRTtnREFDWEEsRUFBRUssY0FBYztnREFDaEJMLEVBQUVNLGVBQWU7Z0RBQ2pCbEQscUJBQXFCOzRDQUN2Qjs0Q0FDQWtILGFBQWEsSUFBTWxILHFCQUFxQjs0Q0FDeENtSCxRQUFRLENBQUN2RTtnREFDUEEsRUFBRUssY0FBYztnREFDaEJMLEVBQUVNLGVBQWU7Z0RBQ2pCcUIsa0JBQWtCekcsV0FBVytHLE1BQU07NENBQ3JDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUVYsOERBQUN1Qjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNHO2dEQUNDRixTQUFTLElBQU0xRixPQUFPdUgsSUFBSSxDQUFDO2dEQUMzQjlCLFdBQVU7Z0RBQ1ZoRixPQUFNOzBEQUVOLDRFQUFDN0Qsb0dBQVFBO29EQUFDNkksV0FBVTs7Ozs7Ozs7Ozs7NENBRXJCeEYsU0FBU3VILFdBQVcsQ0FBQ0MsT0FBTyxrQkFDM0IsOERBQUM3QjtnREFDQ0YsU0FBUyxJQUFNMUYsT0FBT3VILElBQUksQ0FBQztnREFDM0I5QixXQUFVO2dEQUNWaEYsT0FBTTswREFFTiw0RUFBQzlELG9HQUFLQTtvREFBQzhJLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdyQiw4REFBQ0c7Z0RBQ0NGLFNBQVN6SDtnREFDVHdILFdBQVU7Z0RBQ1ZoRixPQUFNOzBEQUVOLDRFQUFDL0Qsb0dBQVFBO29EQUFDK0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3hCLDhEQUFDM0ksbURBQVVBO3dDQUFDMkksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPOUIsOERBQUNHO2dCQUNDRixTQUFTcEk7Z0JBQ1RtSSxXQUFVOzBCQUVWLDRFQUFDcEosb09BQVNBO29CQUFDb0osV0FBVTs7Ozs7Ozs7Ozs7OztBQUk3QjtHQWo4QndCekk7O1FBb0NQakIsc0RBQVNBO1FBQ0hnQixrRUFBV0E7OztLQXJDVkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGV2XFxBSVxcQ2hhdFxcY2hhdDExXFxhaS1jaGF0LWFwcFxcc3JjXFxjb21wb25lbnRzXFxTaWRlYmFyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgQ29udmVyc2F0aW9uLCBXb3Jrc3BhY2UgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xuaW1wb3J0IHtcbiAgUGx1c0ljb24sXG4gIENoYXRCdWJibGVMZWZ0SWNvbixcbiAgVHJhc2hJY29uLFxuICBQZW5jaWxJY29uLFxuICBYTWFya0ljb24sXG4gIEJhcnMzSWNvbixcbiAgQ2hldnJvbkRvd25JY29uLFxuICBDaGV2cm9uUmlnaHRJY29uLFxuICBGb2xkZXJJY29uLFxuICBGb2xkZXJQbHVzSWNvbixcbiAgRWxsaXBzaXNWZXJ0aWNhbEljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IFNldHRpbmdzLCBCcmFpbiwgRmlsZVRleHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5pbXBvcnQgeyBTeW5jU3RhdHVzIH0gZnJvbSAnLi9TeW5jU3RhdHVzJztcbmltcG9ydCB7IHVzZVNldHRpbmdzIH0gZnJvbSAnQC9jb250ZXh0cy9TZXR0aW5nc0NvbnRleHQnO1xuXG5pbnRlcmZhY2UgU2lkZWJhclByb3BzIHtcbiAgY29udmVyc2F0aW9uczogQ29udmVyc2F0aW9uW107XG4gIHdvcmtzcGFjZXM6IFdvcmtzcGFjZVtdO1xuICBjdXJyZW50Q29udmVyc2F0aW9uSWQ6IHN0cmluZyB8IG51bGw7XG4gIGN1cnJlbnRXb3Jrc3BhY2VJZDogc3RyaW5nIHwgbnVsbDtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvblRvZ2dsZTogKCkgPT4gdm9pZDtcbiAgb25OZXdDaGF0OiAod29ya3NwYWNlSWQ/OiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uU2VsZWN0Q29udmVyc2F0aW9uOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25EZWxldGVDb252ZXJzYXRpb246IChpZDogc3RyaW5nKSA9PiB2b2lkO1xuICBvblJlbmFtZUNvbnZlcnNhdGlvbjogKGlkOiBzdHJpbmcsIG5ld1RpdGxlOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uU2VsZWN0V29ya3NwYWNlOiAoaWQ6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG4gIG9uQ3JlYXRlV29ya3NwYWNlOiAod29ya3NwYWNlOiBPbWl0PFdvcmtzcGFjZSwgJ2lkJyB8ICdjcmVhdGVkQXQnIHwgJ3VwZGF0ZWRBdCcgfCAndXNlcklkJz4pID0+IHZvaWQ7XG4gIG9uVXBkYXRlV29ya3NwYWNlOiAod29ya3NwYWNlOiBXb3Jrc3BhY2UpID0+IHZvaWQ7XG4gIG9uRGVsZXRlV29ya3NwYWNlOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgb25SZW9yZGVyV29ya3NwYWNlczogKHdvcmtzcGFjZUlkczogc3RyaW5nW10pID0+IHZvaWQ7XG4gIG9uQXNzaWduQ29udmVyc2F0aW9uVG9Xb3Jrc3BhY2U6IChjb252ZXJzYXRpb25JZDogc3RyaW5nLCB3b3Jrc3BhY2VJZDogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbiAgb25PcGVuU2V0dGluZ3M/OiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaWRlYmFyKHtcbiAgY29udmVyc2F0aW9ucyxcbiAgd29ya3NwYWNlcyxcbiAgY3VycmVudENvbnZlcnNhdGlvbklkLFxuICBjdXJyZW50V29ya3NwYWNlSWQsXG4gIGlzT3BlbixcbiAgb25Ub2dnbGUsXG4gIG9uTmV3Q2hhdCxcbiAgb25TZWxlY3RDb252ZXJzYXRpb24sXG4gIG9uRGVsZXRlQ29udmVyc2F0aW9uLFxuICBvblJlbmFtZUNvbnZlcnNhdGlvbixcbiAgb25TZWxlY3RXb3Jrc3BhY2UsXG4gIG9uQ3JlYXRlV29ya3NwYWNlLFxuICBvblVwZGF0ZVdvcmtzcGFjZSxcbiAgb25EZWxldGVXb3Jrc3BhY2UsXG4gIG9uUmVvcmRlcldvcmtzcGFjZXMsXG4gIG9uQXNzaWduQ29udmVyc2F0aW9uVG9Xb3Jrc3BhY2UsXG4gIG9uT3BlblNldHRpbmdzXG59OiBTaWRlYmFyUHJvcHMpIHtcbiAgY29uc3QgW2VkaXRpbmdJZCwgc2V0RWRpdGluZ0lkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZWRpdFRpdGxlLCBzZXRFZGl0VGl0bGVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaG92ZXJlZElkLCBzZXRIb3ZlcmVkSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtleHBhbmRlZFdvcmtzcGFjZXMsIHNldEV4cGFuZGVkV29ya3NwYWNlc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW3Nob3dXb3Jrc3BhY2VGb3JtLCBzZXRTaG93V29ya3NwYWNlRm9ybV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlZGl0aW5nV29ya3NwYWNlSWQsIHNldEVkaXRpbmdXb3Jrc3BhY2VJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Nob3dDb252ZXJzYXRpb25NZW51LCBzZXRTaG93Q29udmVyc2F0aW9uTWVudV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2RyYWdnZWRXb3Jrc3BhY2UsIHNldERyYWdnZWRXb3Jrc3BhY2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkcmFnT3ZlcldvcmtzcGFjZSwgc2V0RHJhZ092ZXJXb3Jrc3BhY2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtob3ZlcmVkV29ya3NwYWNlLCBzZXRIb3ZlcmVkV29ya3NwYWNlXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbZHJhZ2dlZENvbnZlcnNhdGlvbiwgc2V0RHJhZ2dlZENvbnZlcnNhdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2RyYWdPdmVyVGFyZ2V0LCBzZXREcmFnT3ZlclRhcmdldF0gPSB1c2VTdGF0ZTx7IHR5cGU6ICd3b3Jrc3BhY2UnIHwgJ3VuYXNzaWduZWQnLCBpZD86IHN0cmluZyB9IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt3b3Jrc3BhY2VGb3JtRGF0YSwgc2V0V29ya3NwYWNlRm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIG5hbWU6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBjb2xvcjogJyM2MzY2ZjEnXG4gIH0pO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyBzZXR0aW5ncyB9ID0gdXNlU2V0dGluZ3MoKTtcblxuICAvLyBDbG9zZSBjb252ZXJzYXRpb24gbWVudSB3aGVuIGNsaWNraW5nIG91dHNpZGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoKSA9PiB7XG4gICAgICBzZXRTaG93Q29udmVyc2F0aW9uTWVudShudWxsKTtcbiAgICB9O1xuXG4gICAgaWYgKHNob3dDb252ZXJzYXRpb25NZW51KSB7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgIH1cbiAgfSwgW3Nob3dDb252ZXJzYXRpb25NZW51XSk7XG5cbiAgY29uc3QgaGFuZGxlU3RhcnRFZGl0ID0gKGNvbnZlcnNhdGlvbjogQ29udmVyc2F0aW9uKSA9PiB7XG4gICAgc2V0RWRpdGluZ0lkKGNvbnZlcnNhdGlvbi5pZCk7XG4gICAgc2V0RWRpdFRpdGxlKGNvbnZlcnNhdGlvbi50aXRsZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2F2ZUVkaXQgPSAoKSA9PiB7XG4gICAgaWYgKGVkaXRpbmdJZCAmJiBlZGl0VGl0bGUudHJpbSgpKSB7XG4gICAgICBvblJlbmFtZUNvbnZlcnNhdGlvbihlZGl0aW5nSWQsIGVkaXRUaXRsZS50cmltKCkpO1xuICAgIH1cbiAgICBzZXRFZGl0aW5nSWQobnVsbCk7XG4gICAgc2V0RWRpdFRpdGxlKCcnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWxFZGl0ID0gKCkgPT4ge1xuICAgIHNldEVkaXRpbmdJZChudWxsKTtcbiAgICBzZXRFZGl0VGl0bGUoJycpO1xuICB9O1xuXG4gIC8vIFdvcmtzcGFjZSBoZWxwZXIgZnVuY3Rpb25zXG4gIGNvbnN0IHRvZ2dsZVdvcmtzcGFjZSA9ICh3b3Jrc3BhY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbmV3RXhwYW5kZWQgPSBuZXcgU2V0KGV4cGFuZGVkV29ya3NwYWNlcyk7XG4gICAgaWYgKG5ld0V4cGFuZGVkLmhhcyh3b3Jrc3BhY2VJZCkpIHtcbiAgICAgIG5ld0V4cGFuZGVkLmRlbGV0ZSh3b3Jrc3BhY2VJZCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5ld0V4cGFuZGVkLmFkZCh3b3Jrc3BhY2VJZCk7XG4gICAgfVxuICAgIHNldEV4cGFuZGVkV29ya3NwYWNlcyhuZXdFeHBhbmRlZCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlV29ya3NwYWNlID0gKCkgPT4ge1xuICAgIGlmICh3b3Jrc3BhY2VGb3JtRGF0YS5uYW1lLnRyaW0oKSkge1xuICAgICAgb25DcmVhdGVXb3Jrc3BhY2Uoe1xuICAgICAgICBuYW1lOiB3b3Jrc3BhY2VGb3JtRGF0YS5uYW1lLnRyaW0oKSxcbiAgICAgICAgZGVzY3JpcHRpb246IHdvcmtzcGFjZUZvcm1EYXRhLmRlc2NyaXB0aW9uLnRyaW0oKSB8fCB1bmRlZmluZWQsXG4gICAgICAgIGNvbG9yOiB3b3Jrc3BhY2VGb3JtRGF0YS5jb2xvclxuICAgICAgfSk7XG4gICAgICBzZXRXb3Jrc3BhY2VGb3JtRGF0YSh7IG5hbWU6ICcnLCBkZXNjcmlwdGlvbjogJycsIGNvbG9yOiAnIzYzNjZmMScgfSk7XG4gICAgICBzZXRTaG93V29ya3NwYWNlRm9ybShmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVVwZGF0ZVdvcmtzcGFjZSA9ICgpID0+IHtcbiAgICBpZiAoZWRpdGluZ1dvcmtzcGFjZUlkICYmIHdvcmtzcGFjZUZvcm1EYXRhLm5hbWUudHJpbSgpKSB7XG4gICAgICBjb25zdCB3b3Jrc3BhY2UgPSB3b3Jrc3BhY2VzLmZpbmQodyA9PiB3LmlkID09PSBlZGl0aW5nV29ya3NwYWNlSWQpO1xuICAgICAgaWYgKHdvcmtzcGFjZSkge1xuICAgICAgICBvblVwZGF0ZVdvcmtzcGFjZSh7XG4gICAgICAgICAgLi4ud29ya3NwYWNlLFxuICAgICAgICAgIG5hbWU6IHdvcmtzcGFjZUZvcm1EYXRhLm5hbWUudHJpbSgpLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiB3b3Jrc3BhY2VGb3JtRGF0YS5kZXNjcmlwdGlvbi50cmltKCkgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgIGNvbG9yOiB3b3Jrc3BhY2VGb3JtRGF0YS5jb2xvclxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIHNldEVkaXRpbmdXb3Jrc3BhY2VJZChudWxsKTtcbiAgICAgIHNldFdvcmtzcGFjZUZvcm1EYXRhKHsgbmFtZTogJycsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjNjM2NmYxJyB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc3RhcnRFZGl0V29ya3NwYWNlID0gKHdvcmtzcGFjZTogV29ya3NwYWNlKSA9PiB7XG4gICAgc2V0RWRpdGluZ1dvcmtzcGFjZUlkKHdvcmtzcGFjZS5pZCk7XG4gICAgc2V0V29ya3NwYWNlRm9ybURhdGEoe1xuICAgICAgbmFtZTogd29ya3NwYWNlLm5hbWUsXG4gICAgICBkZXNjcmlwdGlvbjogd29ya3NwYWNlLmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgY29sb3I6IHdvcmtzcGFjZS5jb2xvclxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVdvcmtzcGFjZSA9ICh3b3Jrc3BhY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgdGhpcyB3b3Jrc3BhY2U/IENvbnZlcnNhdGlvbnMgd2lsbCBiZSBtb3ZlZCB0byB1bmFzc2lnbmVkLicpKSB7XG4gICAgICBvbkRlbGV0ZVdvcmtzcGFjZSh3b3Jrc3BhY2VJZCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZUNvbnZlcnNhdGlvbkluV29ya3NwYWNlID0gKHdvcmtzcGFjZUlkOiBzdHJpbmcpID0+IHtcbiAgICAvLyBDcmVhdGUgYSBuZXcgY29udmVyc2F0aW9uIGFuZCBhc3NpZ24gaXQgdG8gdGhlIHdvcmtzcGFjZVxuICAgIG9uTmV3Q2hhdCh3b3Jrc3BhY2VJZCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQXNzaWduQ29udmVyc2F0aW9uID0gKGNvbnZlcnNhdGlvbklkOiBzdHJpbmcsIHdvcmtzcGFjZUlkOiBzdHJpbmcgfCBudWxsKSA9PiB7XG4gICAgb25Bc3NpZ25Db252ZXJzYXRpb25Ub1dvcmtzcGFjZShjb252ZXJzYXRpb25JZCwgd29ya3NwYWNlSWQpO1xuICAgIHNldFNob3dDb252ZXJzYXRpb25NZW51KG51bGwpO1xuICB9O1xuXG4gIC8vIERyYWcgYW5kIGRyb3AgaGFuZGxlcnMgZm9yIHdvcmtzcGFjZSByZW9yZGVyaW5nXG4gIGNvbnN0IGhhbmRsZURyYWdTdGFydCA9IChlOiBSZWFjdC5EcmFnRXZlbnQsIHdvcmtzcGFjZUlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXREcmFnZ2VkV29ya3NwYWNlKHdvcmtzcGFjZUlkKTtcbiAgICBlLmRhdGFUcmFuc2Zlci5lZmZlY3RBbGxvd2VkID0gJ21vdmUnO1xuICAgIGUuZGF0YVRyYW5zZmVyLnNldERhdGEoJ3RleHQvcGxhaW4nLCB3b3Jrc3BhY2VJZCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRHJhZ092ZXIgPSAoZTogUmVhY3QuRHJhZ0V2ZW50LCB3b3Jrc3BhY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgZS5kYXRhVHJhbnNmZXIuZHJvcEVmZmVjdCA9ICdtb3ZlJztcblxuICAgIGlmIChkcmFnZ2VkV29ya3NwYWNlICYmIGRyYWdnZWRXb3Jrc3BhY2UgIT09IHdvcmtzcGFjZUlkKSB7XG4gICAgICAvLyBEZXRlcm1pbmUgaWYgd2Ugc2hvdWxkIGluc2VydCBhYm92ZSBvciBiZWxvdyBiYXNlZCBvbiBtb3VzZSBwb3NpdGlvblxuICAgICAgY29uc3QgcmVjdCA9IChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEVsZW1lbnQpLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgICAgY29uc3QgbWlkWSA9IHJlY3QudG9wICsgcmVjdC5oZWlnaHQgLyAyO1xuICAgICAgY29uc3QgaW5zZXJ0QWJvdmUgPSBlLmNsaWVudFkgPCBtaWRZO1xuXG4gICAgICBzZXREcmFnT3ZlcldvcmtzcGFjZSh3b3Jrc3BhY2VJZCk7XG4gICAgICAvLyBTdG9yZSB0aGUgaW5zZXJ0IHBvc2l0aW9uIGZvciB2aXN1YWwgZmVlZGJhY2tcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEVsZW1lbnQpLmRhdGFzZXQuaW5zZXJ0QWJvdmUgPSBpbnNlcnRBYm92ZS50b1N0cmluZygpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnTGVhdmUgPSAoZTogUmVhY3QuRHJhZ0V2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldERyYWdPdmVyV29ya3NwYWNlKG51bGwpO1xuICAgIC8vIENsZWFyIHRoZSBpbnNlcnQgcG9zaXRpb25cbiAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxFbGVtZW50KS5kYXRhc2V0Lmluc2VydEFib3ZlID0gJyc7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRHJhZ0VuZCA9ICgpID0+IHtcbiAgICBzZXREcmFnZ2VkV29ya3NwYWNlKG51bGwpO1xuICAgIHNldERyYWdPdmVyV29ya3NwYWNlKG51bGwpO1xuICAgIHNldERyYWdnZWRDb252ZXJzYXRpb24obnVsbCk7XG4gICAgc2V0RHJhZ092ZXJUYXJnZXQobnVsbCk7XG4gIH07XG5cbiAgLy8gQ29udmVyc2F0aW9uIGRyYWcgYW5kIGRyb3AgaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlQ29udmVyc2F0aW9uRHJhZ1N0YXJ0ID0gKGU6IFJlYWN0LkRyYWdFdmVudCwgY29udmVyc2F0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgIHNldERyYWdnZWRDb252ZXJzYXRpb24oY29udmVyc2F0aW9uSWQpO1xuICAgIGUuZGF0YVRyYW5zZmVyLmVmZmVjdEFsbG93ZWQgPSAnbW92ZSc7XG4gICAgZS5kYXRhVHJhbnNmZXIuc2V0RGF0YSgndGV4dC9wbGFpbicsIGNvbnZlcnNhdGlvbklkKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVXb3Jrc3BhY2VEcm9wWm9uZSA9IChlOiBSZWFjdC5EcmFnRXZlbnQsIHdvcmtzcGFjZUlkPzogc3RyaW5nKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG5cbiAgICBpZiAoZHJhZ2dlZENvbnZlcnNhdGlvbikge1xuICAgICAgc2V0RHJhZ092ZXJUYXJnZXQoeyB0eXBlOiB3b3Jrc3BhY2VJZCA/ICd3b3Jrc3BhY2UnIDogJ3VuYXNzaWduZWQnLCBpZDogd29ya3NwYWNlSWQgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvbnZlcnNhdGlvbkRyb3AgPSBhc3luYyAoZTogUmVhY3QuRHJhZ0V2ZW50LCB0YXJnZXRXb3Jrc3BhY2VJZD86IHN0cmluZykgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuXG4gICAgaWYgKCFkcmFnZ2VkQ29udmVyc2F0aW9uKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgYXdhaXQgb25Bc3NpZ25Db252ZXJzYXRpb25Ub1dvcmtzcGFjZShkcmFnZ2VkQ29udmVyc2F0aW9uLCB0YXJnZXRXb3Jrc3BhY2VJZCB8fCBudWxsKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIG1vdmUgY29udmVyc2F0aW9uOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0RHJhZ2dlZENvbnZlcnNhdGlvbihudWxsKTtcbiAgICAgIHNldERyYWdPdmVyVGFyZ2V0KG51bGwpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wQmV0d2VlbiA9IGFzeW5jIChpbnNlcnRJbmRleDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKCFkcmFnZ2VkV29ya3NwYWNlKSB7XG4gICAgICBzZXREcmFnZ2VkV29ya3NwYWNlKG51bGwpO1xuICAgICAgc2V0RHJhZ092ZXJXb3Jrc3BhY2UobnVsbCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpbmQgdGhlIGluZGV4IG9mIHRoZSBkcmFnZ2VkIHdvcmtzcGFjZVxuICAgICAgY29uc3QgZHJhZ2dlZEluZGV4ID0gd29ya3NwYWNlcy5maW5kSW5kZXgodyA9PiB3LmlkID09PSBkcmFnZ2VkV29ya3NwYWNlKTtcblxuICAgICAgaWYgKGRyYWdnZWRJbmRleCA9PT0gLTEpIHJldHVybjtcblxuICAgICAgY29uc29sZS5sb2coJ1NpZGViYXI6IGhhbmRsZURyb3BCZXR3ZWVuIGNhbGxlZCcsIHtcbiAgICAgICAgZHJhZ2dlZFdvcmtzcGFjZSxcbiAgICAgICAgZHJhZ2dlZEluZGV4LFxuICAgICAgICBpbnNlcnRJbmRleCxcbiAgICAgICAgdG90YWxXb3Jrc3BhY2VzOiB3b3Jrc3BhY2VzLmxlbmd0aCxcbiAgICAgICAgd29ya3NwYWNlSWRzOiB3b3Jrc3BhY2VzLm1hcCh3ID0+IHcuaWQpXG4gICAgICB9KTtcblxuICAgICAgLy8gQWRqdXN0IGluc2VydCBpbmRleCBpZiBkcmFnZ2luZyBkb3duXG4gICAgICBsZXQgYWRqdXN0ZWRJbmRleCA9IGluc2VydEluZGV4O1xuICAgICAgaWYgKGRyYWdnZWRJbmRleCA8IGluc2VydEluZGV4KSB7XG4gICAgICAgIGFkanVzdGVkSW5kZXggPSBpbnNlcnRJbmRleCAtIDE7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdTaWRlYmFyOiBBZGp1c3RlZCBpbmRleDonLCBhZGp1c3RlZEluZGV4KTtcblxuICAgICAgLy8gQ2hlY2sgaWYgdGhlIHBvc2l0aW9uIGlzIGFjdHVhbGx5IGNoYW5naW5nXG4gICAgICBpZiAoZHJhZ2dlZEluZGV4ID09PSBhZGp1c3RlZEluZGV4KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdTaWRlYmFyOiBObyBwb3NpdGlvbiBjaGFuZ2UgbmVlZGVkLCBza2lwcGluZyByZW9yZGVyJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIG5ldyBvcmRlclxuICAgICAgY29uc3QgbmV3V29ya3NwYWNlcyA9IFsuLi53b3Jrc3BhY2VzXTtcbiAgICAgIGNvbnN0IFtkcmFnZ2VkSXRlbV0gPSBuZXdXb3Jrc3BhY2VzLnNwbGljZShkcmFnZ2VkSW5kZXgsIDEpO1xuICAgICAgbmV3V29ya3NwYWNlcy5zcGxpY2UoYWRqdXN0ZWRJbmRleCwgMCwgZHJhZ2dlZEl0ZW0pO1xuXG4gICAgICAvLyBVcGRhdGUgcG9zaXRpb25zIGFuZCBjYWxsIHJlb3JkZXIgQVBJXG4gICAgICBjb25zdCB3b3Jrc3BhY2VJZHMgPSBuZXdXb3Jrc3BhY2VzLm1hcCh3ID0+IHcuaWQpO1xuXG4gICAgICBjb25zb2xlLmxvZygnU2lkZWJhcjogQ2FsbGluZyByZW9yZGVyIHdpdGggbmV3IG9yZGVyOicsIHdvcmtzcGFjZUlkcyk7XG4gICAgICBhd2FpdCBvblJlb3JkZXJXb3Jrc3BhY2VzKHdvcmtzcGFjZUlkcyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZW9yZGVyIHdvcmtzcGFjZXM6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXREcmFnZ2VkV29ya3NwYWNlKG51bGwpO1xuICAgICAgc2V0RHJhZ092ZXJXb3Jrc3BhY2UobnVsbCk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEdyb3VwIGNvbnZlcnNhdGlvbnMgYnkgd29ya3NwYWNlXG4gIGNvbnN0IHVuYXNzaWduZWRDb252ZXJzYXRpb25zID0gY29udmVyc2F0aW9ucy5maWx0ZXIoY29udiA9PiAhY29udi53b3Jrc3BhY2VJZCk7XG4gIGNvbnN0IHdvcmtzcGFjZUNvbnZlcnNhdGlvbnMgPSB3b3Jrc3BhY2VzLnJlZHVjZSgoYWNjLCB3b3Jrc3BhY2UpID0+IHtcbiAgICBhY2Nbd29ya3NwYWNlLmlkXSA9IGNvbnZlcnNhdGlvbnMuZmlsdGVyKGNvbnYgPT4gY29udi53b3Jrc3BhY2VJZCA9PT0gd29ya3NwYWNlLmlkKTtcbiAgICByZXR1cm4gYWNjO1xuICB9LCB7fSBhcyBSZWNvcmQ8c3RyaW5nLCBDb252ZXJzYXRpb25bXT4pO1xuXG5cblxuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAodGltZXN0YW1wOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IGRpZmZJbkRheXMgPSBNYXRoLmZsb29yKChub3cuZ2V0VGltZSgpIC0gZGF0ZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcbiAgICBcbiAgICBpZiAoZGlmZkluRGF5cyA9PT0gMCkge1xuICAgICAgcmV0dXJuICdUb2RheSc7XG4gICAgfSBlbHNlIGlmIChkaWZmSW5EYXlzID09PSAxKSB7XG4gICAgICByZXR1cm4gJ1llc3RlcmRheSc7XG4gICAgfSBlbHNlIGlmIChkaWZmSW5EYXlzIDwgNykge1xuICAgICAgcmV0dXJuIGAke2RpZmZJbkRheXN9IGRheXMgYWdvYDtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBNb2JpbGUgb3ZlcmxheSAqL31cbiAgICAgIHtpc09wZW4gJiYgKFxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCB6LTQwIG1kOmhpZGRlblwiXG4gICAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgICAgXG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmaXhlZCBsZWZ0LTAgdG9wLTAgaC1mdWxsIGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgei01MCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXRcIixcbiAgICAgICAgXCJ3LTk2IG1kOnctODBcIixcbiAgICAgICAgaXNPcGVuID8gXCJ0cmFuc2xhdGUteC0wXCIgOiBcIi10cmFuc2xhdGUteC1mdWxsXCIsXG4gICAgICAgIFwibWQ6cmVsYXRpdmUgbWQ6dHJhbnNsYXRlLXgtMFwiXG4gICAgICApfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci1iIGJvcmRlci1zaWRlYmFyLWJvcmRlclwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkXCI+TmV4dXM8L2gxPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IHJvdW5kZWQtbGcgbWQ6aGlkZGVuXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5ldyBDaGF0IEJ1dHRvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbk5ld0NoYXR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBwLTMgYmctc2lkZWJhci1hY2NlbnQgaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQvODAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgTmV3IENoYXRcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFdvcmtzcGFjZXMgYW5kIENvbnZlcnNhdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHB4LTQgcGItNFwiPlxuICAgICAgICAgICAgey8qIFdvcmtzcGFjZSBNYW5hZ2VtZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTNcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNpZGViYXItbXV0ZWRcIj5Xb3Jrc3BhY2VzPC9oMz5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93V29ya3NwYWNlRm9ybSh0cnVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ3JlYXRlIHdvcmtzcGFjZVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEZvbGRlclBsdXNJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogV29ya3NwYWNlIENyZWF0aW9uIEZvcm0gKi99XG4gICAgICAgICAgICAgIHtzaG93V29ya3NwYWNlRm9ybSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zIHAtMyBiZy1zaWRlYmFyLWFjY2VudC81MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldvcmtzcGFjZSBuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3dvcmtzcGFjZUZvcm1EYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0V29ya3NwYWNlRm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1zaWRlYmFyIHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gbWItMlwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmlwdGlvbiAob3B0aW9uYWwpXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3dvcmtzcGFjZUZvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFdvcmtzcGFjZUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBtYi0yXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17d29ya3NwYWNlRm9ybURhdGEuY29sb3J9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRXb3Jrc3BhY2VGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGNvbG9yOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy04IGgtOCByb3VuZGVkIGJvcmRlciBib3JkZXItc2lkZWJhci1ib3JkZXJcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2lkZWJhci1tdXRlZFwiPkNvbG9yPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNyZWF0ZVdvcmtzcGFjZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCByb3VuZGVkLW1kIHRleHQtc20gaG92ZXI6YmctcHJpbWFyeS85MFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBDcmVhdGVcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93V29ya3NwYWNlRm9ybShmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRXb3Jrc3BhY2VGb3JtRGF0YSh7IG5hbWU6ICcnLCBkZXNjcmlwdGlvbjogJycsIGNvbG9yOiAnIzYzNjZmMScgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctc2lkZWJhci1hY2NlbnQgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgcm91bmRlZC1tZCB0ZXh0LXNtIGhvdmVyOmJnLXNpZGViYXItYWNjZW50LzgwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBXb3Jrc3BhY2UgRWRpdCBGb3JtICovfVxuICAgICAgICAgICAgICB7ZWRpdGluZ1dvcmtzcGFjZUlkICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTMgcC0zIGJnLXNpZGViYXItYWNjZW50LzUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV29ya3NwYWNlIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17d29ya3NwYWNlRm9ybURhdGEubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRXb3Jrc3BhY2VGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXNpZGViYXIgdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBtYi0yXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaXB0aW9uIChvcHRpb25hbClcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17d29ya3NwYWNlRm9ybURhdGEuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0V29ya3NwYWNlRm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBkZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctc2lkZWJhciB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIG1iLTJcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY29sb3JcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt3b3Jrc3BhY2VGb3JtRGF0YS5jb2xvcn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFdvcmtzcGFjZUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY29sb3I6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTggaC04IHJvdW5kZWQgYm9yZGVyIGJvcmRlci1zaWRlYmFyLWJvcmRlclwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zaWRlYmFyLW11dGVkXCI+Q29sb3I8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVXBkYXRlV29ya3NwYWNlfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHJvdW5kZWQtbWQgdGV4dC1zbSBob3ZlcjpiZy1wcmltYXJ5LzkwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIFVwZGF0ZVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEVkaXRpbmdXb3Jrc3BhY2VJZChudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFdvcmtzcGFjZUZvcm1EYXRhKHsgbmFtZTogJycsIGRlc2NyaXB0aW9uOiAnJywgY29sb3I6ICcjNjM2NmYxJyB9KTtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1zaWRlYmFyLWFjY2VudCB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCByb3VuZGVkLW1kIHRleHQtc20gaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQvODBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFdvcmtzcGFjZXMgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIHt3b3Jrc3BhY2VzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNpZGViYXItbXV0ZWQgbWItMyBweC0xXCI+TXkgV29ya3NwYWNlczwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBEcm9wIHpvbmUgYWJvdmUgZmlyc3Qgd29ya3NwYWNlICovfVxuICAgICAgICAgICAgICAgICAge2RyYWdnZWRXb3Jrc3BhY2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiaC0yIC1tYi0xIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgZHJhZ092ZXJXb3Jrc3BhY2UgPT09ICdhYm92ZS1maXJzdC13b3Jrc3BhY2UnICYmIFwiYmctYmx1ZS01MDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldERyYWdPdmVyV29ya3NwYWNlKCdhYm92ZS1maXJzdC13b3Jrc3BhY2UnKTtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ0xlYXZlPXsoKSA9PiBzZXREcmFnT3ZlcldvcmtzcGFjZShudWxsKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcEJldHdlZW4oMCk7IC8vIEluc2VydCBhdCB0aGUgYmVnaW5uaW5nXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBXb3Jrc3BhY2UgSXRlbXMgKi99XG4gICAgICAgICAgICAgICAgICB7d29ya3NwYWNlcy5tYXAoKHdvcmtzcGFjZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3dvcmtzcGFjZS5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIERyb3Agem9uZSBhYm92ZSB3b3Jrc3BhY2UgKGV4Y2VwdCBmaXJzdCBvbmUpICovfVxuICAgICAgICAgICAgICAgICAgICAgIHtkcmFnZ2VkV29ya3NwYWNlICYmIGRyYWdnZWRXb3Jrc3BhY2UgIT09IHdvcmtzcGFjZS5pZCAmJiBpbmRleCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiaC0yIC1tYi0xIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdPdmVyV29ya3NwYWNlID09PSBgYWJvdmUtJHt3b3Jrc3BhY2UuaWR9YCAmJiBcImJnLWJsdWUtNTAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldERyYWdPdmVyV29ya3NwYWNlKGBhYm92ZS0ke3dvcmtzcGFjZS5pZH1gKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25EcmFnTGVhdmU9eygpID0+IHNldERyYWdPdmVyV29ya3NwYWNlKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcEJldHdlZW4oaW5kZXgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCJncm91cCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgcmVsYXRpdmUgY3Vyc29yLW1vdmVcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZHJhZ2dlZFdvcmtzcGFjZSA9PT0gd29ya3NwYWNlLmlkICYmIFwib3BhY2l0eS01MCBzY2FsZS05NVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBkcmFnZ2VkV29ya3NwYWNlICYmIGRyYWdnZWRXb3Jrc3BhY2UgIT09IHdvcmtzcGFjZS5pZCAmJiBcIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZT17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ1N0YXJ0PXsoZSkgPT4gaGFuZGxlRHJhZ1N0YXJ0KGUsIHdvcmtzcGFjZS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdFbmQ9e2hhbmRsZURyYWdFbmR9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEhvdmVyZWRXb3Jrc3BhY2Uod29ya3NwYWNlLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KCkgPT4gc2V0SG92ZXJlZFdvcmtzcGFjZShudWxsKX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IHdvcmtzcGFjZS5jb2xvciB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3dvcmtzcGFjZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNpZGViYXItbXV0ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKHt3b3Jrc3BhY2VDb252ZXJzYXRpb25zW3dvcmtzcGFjZS5pZF0/Lmxlbmd0aCB8fCAwfSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMSBtbC1hdXRvXCIgZHJhZ2dhYmxlPXtmYWxzZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVDcmVhdGVDb252ZXJzYXRpb25JbldvcmtzcGFjZSh3b3Jrc3BhY2UuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcInAtMSBob3ZlcjpiZy1ncmVlbi01MDAvMjAgcm91bmRlZC1tZCB0ZXh0LWdyZWVuLTQwMCBob3Zlcjp0ZXh0LWdyZWVuLTMwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvdmVyZWRXb3Jrc3BhY2UgPT09IHdvcmtzcGFjZS5pZCA/IFwib3BhY2l0eS0xMDBcIiA6IFwib3BhY2l0eS0wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiTmV3IGNvbnZlcnNhdGlvbiBpbiB3b3Jrc3BhY2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZT17ZmFsc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZURvd249eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBsdXNJY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHN0YXJ0RWRpdFdvcmtzcGFjZSh3b3Jrc3BhY2UpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcInAtMSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCByb3VuZGVkLW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaG92ZXJlZFdvcmtzcGFjZSA9PT0gd29ya3NwYWNlLmlkID8gXCJvcGFjaXR5LTEwMFwiIDogXCJvcGFjaXR5LTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0IHdvcmtzcGFjZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZHJhZ2dhYmxlPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRG93bj17KGUpID0+IGUuc3RvcFByb3BhZ2F0aW9uKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGVuY2lsSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVXb3Jrc3BhY2Uod29ya3NwYWNlLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJwLTEgaG92ZXI6YmctcmVkLTUwMC8yMCByb3VuZGVkLW1kIHRleHQtcmVkLTQwMCBob3Zlcjp0ZXh0LXJlZC0zMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3ZlcmVkV29ya3NwYWNlID09PSB3b3Jrc3BhY2UuaWQgPyBcIm9wYWNpdHktMTAwXCIgOiBcIm9wYWNpdHktMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkRlbGV0ZSB3b3Jrc3BhY2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZT17ZmFsc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZURvd249eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgICAgey8qIERyb3Agem9uZSBiZWxvdyBhbGwgd29ya3NwYWNlcyAqL31cbiAgICAgICAgICAgICAgICAgIHtkcmFnZ2VkV29ya3NwYWNlICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICBcImgtMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdPdmVyV29ya3NwYWNlID09PSAnYmVsb3ctYWxsLXdvcmtzcGFjZXMnICYmIFwiYmctYmx1ZS01MDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldERyYWdPdmVyV29ya3NwYWNlKCdiZWxvdy1hbGwtd29ya3NwYWNlcycpO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgb25EcmFnTGVhdmU9eygpID0+IHNldERyYWdPdmVyV29ya3NwYWNlKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICAgIG9uRHJvcD17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wQmV0d2Vlbih3b3Jrc3BhY2VzLmxlbmd0aCk7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIENvbnZlcnNhdGlvbnMgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIHtjb252ZXJzYXRpb25zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNpZGViYXItbXV0ZWQgdGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgIE5vIGNvbnZlcnNhdGlvbnMgeWV0XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNpZGViYXItbXV0ZWQgbWItMyBweC0xXCI+Q29udmVyc2F0aW9uczwvaDM+XG5cbiAgICAgICAgICAgICAgICB7LyogVW5hc3NpZ25lZCBDb252ZXJzYXRpb25zICovfVxuICAgICAgICAgICAgICAgIHt1bmFzc2lnbmVkQ29udmVyc2F0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yIHAtMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBkcmFnT3ZlclRhcmdldD8udHlwZSA9PT0gJ3VuYXNzaWduZWQnICYmIFwiYmctZ3JlZW4tNTAwLzIwIGJvcmRlci0yIGJvcmRlci1ncmVlbi01MDAgYm9yZGVyLWRhc2hlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4gZHJhZ2dlZENvbnZlcnNhdGlvbiAmJiBoYW5kbGVXb3Jrc3BhY2VEcm9wWm9uZShlKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiBkcmFnZ2VkQ29udmVyc2F0aW9uICYmIGhhbmRsZUNvbnZlcnNhdGlvbkRyb3AoZSl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hhdEJ1YmJsZUxlZnRJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1zaWRlYmFyLW11dGVkXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtc2lkZWJhci1tdXRlZFwiPlVuYXNzaWduZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNpZGViYXItbXV0ZWRcIj4oe3VuYXNzaWduZWRDb252ZXJzYXRpb25zLmxlbmd0aH0pPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWwtNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt1bmFzc2lnbmVkQ29udmVyc2F0aW9ucy5tYXAoKGNvbnZlcnNhdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NvbnZlcnNhdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImdyb3VwIHJlbGF0aXZlIHAtMiByb3VuZGVkLWxnIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tY29sb3JzXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudENvbnZlcnNhdGlvbklkID09PSBjb252ZXJzYXRpb24uaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1zaWRlYmFyLWFjY2VudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQvNTBcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkcmFnZ2VkQ29udmVyc2F0aW9uID09PSBjb252ZXJzYXRpb24uaWQgJiYgXCJvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZHJhZ2dhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ1N0YXJ0PXsoZSkgPT4gaGFuZGxlQ29udmVyc2F0aW9uRHJhZ1N0YXJ0KGUsIGNvbnZlcnNhdGlvbi5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ0VuZD17aGFuZGxlRHJhZ0VuZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25TZWxlY3RDb252ZXJzYXRpb24oY29udmVyc2F0aW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBzZXRIb3ZlcmVkSWQoY29udmVyc2F0aW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRIb3ZlcmVkSWQobnVsbCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nSWQgPT09IGNvbnZlcnNhdGlvbi5pZCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlZGl0VGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0VGl0bGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cj17aGFuZGxlU2F2ZUVkaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUua2V5ID09PSAnRW50ZXInKSBoYW5kbGVTYXZlRWRpdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUua2V5ID09PSAnRXNjYXBlJykgaGFuZGxlQ2FuY2VsRWRpdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXNpZGViYXItYWNjZW50IHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kIHB4LTIgcHktMSByb3VuZGVkIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb252ZXJzYXRpb24udGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2lkZWJhci1tdXRlZCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShjb252ZXJzYXRpb24udXBkYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIEFjdGlvbiBidXR0b25zICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiZmxleCBnYXAtMSB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3ZlcmVkSWQgPT09IGNvbnZlcnNhdGlvbi5pZCA/IFwib3BhY2l0eS0xMDBcIiA6IFwib3BhY2l0eS0wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVN0YXJ0RWRpdChjb252ZXJzYXRpb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCBjb252ZXJzYXRpb25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGVuY2lsSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93Q29udmVyc2F0aW9uTWVudShcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hvd0NvbnZlcnNhdGlvbk1lbnUgPT09IGNvbnZlcnNhdGlvbi5pZCA/IG51bGwgOiBjb252ZXJzYXRpb24uaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJNb3ZlIHRvIHdvcmtzcGFjZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RWxsaXBzaXNWZXJ0aWNhbEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBXb3Jrc3BhY2UgYXNzaWdubWVudCBkcm9wZG93biAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dDb252ZXJzYXRpb25NZW51ID09PSBjb252ZXJzYXRpb24uaWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtZnVsbCBtdC0xIGJnLXNpZGViYXIgYm9yZGVyIGJvcmRlci1zaWRlYmFyLWJvcmRlciByb3VuZGVkLW1kIHNoYWRvdy1sZyB6LTUwIG1pbi13LVsxNjBweF1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVBc3NpZ25Db252ZXJzYXRpb24oY29udmVyc2F0aW9uLmlkLCBudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcHgtMyBweS0yIHRleHQtc20gaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TnSBVbmFzc2lnbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7d29ya3NwYWNlcy5tYXAoKHdzKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt3cy5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUFzc2lnbkNvbnZlcnNhdGlvbihjb252ZXJzYXRpb24uaWQsIHdzLmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHB4LTMgcHktMiB0ZXh0LXNtIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMiBoLTIgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IHdzLmNvbG9yIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3dzLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVsZXRlQ29udmVyc2F0aW9uKGNvbnZlcnNhdGlvbi5pZCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1yZWQtNTAwLzIwIHJvdW5kZWQtbWQgdGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRlIGNvbnZlcnNhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgey8qIFdvcmtzcGFjZSBDb252ZXJzYXRpb25zICovfVxuICAgICAgICAgICAgICAgIHt3b3Jrc3BhY2VzLm1hcCgod29ya3NwYWNlKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17d29ya3NwYWNlLmlkfT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICBcImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTIgcC0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdPdmVyVGFyZ2V0Py50eXBlID09PSAnd29ya3NwYWNlJyAmJiBkcmFnT3ZlclRhcmdldC5pZCA9PT0gd29ya3NwYWNlLmlkICYmIFwiYmctZ3JlZW4tNTAwLzIwIGJvcmRlci0yIGJvcmRlci1ncmVlbi01MDAgYm9yZGVyLWRhc2hlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRyYWdnZWRDb252ZXJzYXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlV29ya3NwYWNlRHJvcFpvbmUoZSwgd29ya3NwYWNlLmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ0xlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRyYWdnZWRDb252ZXJzYXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0RHJhZ092ZXJUYXJnZXQobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZHJhZ2dlZENvbnZlcnNhdGlvbikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDb252ZXJzYXRpb25Ecm9wKGUsIHdvcmtzcGFjZS5pZCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZVdvcmtzcGFjZSh3b3Jrc3BhY2UuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0wLjUgaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2V4cGFuZGVkV29ya3NwYWNlcy5oYXMod29ya3NwYWNlLmlkKSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2lkZWJhci1tdXRlZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0SWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2lkZWJhci1tdXRlZFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMyBoLTMgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiB3b3Jrc3BhY2UuY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zaWRlYmFyLWZvcmVncm91bmQgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt3b3Jrc3BhY2UubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNpZGViYXItbXV0ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICh7d29ya3NwYWNlQ29udmVyc2F0aW9uc1t3b3Jrc3BhY2UuaWRdPy5sZW5ndGggfHwgMH0pXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7ZXhwYW5kZWRXb3Jrc3BhY2VzLmhhcyh3b3Jrc3BhY2UuaWQpICYmIHdvcmtzcGFjZUNvbnZlcnNhdGlvbnNbd29ya3NwYWNlLmlkXSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgbWwtNiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7d29ya3NwYWNlQ29udmVyc2F0aW9uc1t3b3Jrc3BhY2UuaWRdLm1hcCgoY29udmVyc2F0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NvbnZlcnNhdGlvbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJncm91cCByZWxhdGl2ZSBwLTIgcm91bmRlZC1sZyBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudENvbnZlcnNhdGlvbklkID09PSBjb252ZXJzYXRpb24uaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLXNpZGViYXItYWNjZW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImhvdmVyOmJnLXNpZGViYXItYWNjZW50LzUwXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkcmFnZ2VkQ29udmVyc2F0aW9uID09PSBjb252ZXJzYXRpb24uaWQgJiYgXCJvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ1N0YXJ0PXsoZSkgPT4gaGFuZGxlQ29udmVyc2F0aW9uRHJhZ1N0YXJ0KGUsIGNvbnZlcnNhdGlvbi5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EcmFnRW5kPXtoYW5kbGVEcmFnRW5kfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU2VsZWN0Q29udmVyc2F0aW9uKGNvbnZlcnNhdGlvbi5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBzZXRIb3ZlcmVkSWQoY29udmVyc2F0aW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IHNldEhvdmVyZWRJZChudWxsKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZWRpdGluZ0lkID09PSBjb252ZXJzYXRpb24uaWQgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdFRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0VGl0bGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25CbHVyPXtoYW5kbGVTYXZlRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uS2V5RG93bj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUua2V5ID09PSAnRW50ZXInKSBoYW5kbGVTYXZlRWRpdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS5rZXkgPT09ICdFc2NhcGUnKSBoYW5kbGVDYW5jZWxFZGl0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXNpZGViYXItYWNjZW50IHRleHQtc2lkZWJhci1mb3JlZ3JvdW5kIHB4LTIgcHktMSByb3VuZGVkIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbnZlcnNhdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2lkZWJhci1tdXRlZCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGNvbnZlcnNhdGlvbi51cGRhdGVkQXQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIEFjdGlvbiBidXR0b25zICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcImZsZXggZ2FwLTEgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBob3ZlcmVkSWQgPT09IGNvbnZlcnNhdGlvbi5pZCA/IFwib3BhY2l0eS0xMDBcIiA6IFwib3BhY2l0eS0wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTdGFydEVkaXQoY29udmVyc2F0aW9uKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVkaXQgY29udmVyc2F0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQZW5jaWxJY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNob3dDb252ZXJzYXRpb25NZW51KFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dDb252ZXJzYXRpb25NZW51ID09PSBjb252ZXJzYXRpb24uaWQgPyBudWxsIDogY29udmVyc2F0aW9uLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJNb3ZlIHRvIHdvcmtzcGFjZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVsbGlwc2lzVmVydGljYWxJY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFdvcmtzcGFjZSBhc3NpZ25tZW50IGRyb3Bkb3duICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93Q29udmVyc2F0aW9uTWVudSA9PT0gY29udmVyc2F0aW9uLmlkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtZnVsbCBtdC0xIGJnLXNpZGViYXIgYm9yZGVyIGJvcmRlci1zaWRlYmFyLWJvcmRlciByb3VuZGVkLW1kIHNoYWRvdy1sZyB6LTUwIG1pbi13LVsxNjBweF1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQXNzaWduQ29udmVyc2F0aW9uKGNvbnZlcnNhdGlvbi5pZCwgbnVsbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBweC0zIHB5LTIgdGV4dC1zbSBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TnSBVbmFzc2lnbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3dvcmtzcGFjZXMubWFwKCh3cykgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3dzLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQXNzaWduQ29udmVyc2F0aW9uKGNvbnZlcnNhdGlvbi5pZCwgd3MuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHB4LTMgcHktMiB0ZXh0LXNtIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMiBoLTIgcm91bmRlZC1mdWxsIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogd3MuY29sb3IgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3dzLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWxldGVDb252ZXJzYXRpb24oY29udmVyc2F0aW9uLmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1yZWQtNTAwLzIwIHJvdW5kZWQtbWQgdGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTMwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgY29udmVyc2F0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaEljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgIHsvKiBEcm9wIHpvbmUgYmVsb3cgYWxsIHdvcmtzcGFjZXMgKi99XG4gICAgICAgICAgICAgICAge2RyYWdnZWRXb3Jrc3BhY2UgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgICAgIFwiaC0yIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRyYWdPdmVyV29ya3NwYWNlID09PSAnYmVsb3ctYWxsJyAmJiBcImJnLWJsdWUtNTAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIHNldERyYWdPdmVyV29ya3NwYWNlKCdiZWxvdy1hbGwnKTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgb25EcmFnTGVhdmU9eygpID0+IHNldERyYWdPdmVyV29ya3NwYWNlKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcEJldHdlZW4od29ya3NwYWNlcy5sZW5ndGgpO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQm90dG9tIFRvb2xiYXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIHAtMyBib3JkZXItdCBib3JkZXItc2lkZWJhci1ib3JkZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2ZpbGVzJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgaG92ZXI6Ymctc2lkZWJhci1hY2NlbnQgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkZpbGUgTWFuYWdlclwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1zaWRlYmFyLW11dGVkIGhvdmVyOnRleHQtc2lkZWJhci1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICB7c2V0dGluZ3MuemVwU2V0dGluZ3MuZW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvbWVtb3J5Jyl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiBob3ZlcjpiZy1zaWRlYmFyLWFjY2VudCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJNZW1vcnkgTWFuYWdlbWVudFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxCcmFpbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtc2lkZWJhci1tdXRlZCBob3Zlcjp0ZXh0LXNpZGViYXItZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uT3BlblNldHRpbmdzfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGhvdmVyOmJnLXNpZGViYXItYWNjZW50IHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTZXR0aW5nc1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1zaWRlYmFyLW11dGVkIGhvdmVyOnRleHQtc2lkZWJhci1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFN0YXR1cyBpbmRpY2F0b3IgLSBvbmx5IHNob3cgd2hlbiB0aGVyZSdzIGFuIGlzc3VlICovfVxuICAgICAgICAgICAgICA8U3luY1N0YXR1cyBjbGFzc05hbWU9XCJ0ZXh0LXhzXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTW9iaWxlIHRvZ2dsZSBidXR0b24gKi99XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlfVxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCBsZWZ0LTQgei0zMCBwLTIgYmctc2lkZWJhciB0ZXh0LXNpZGViYXItZm9yZWdyb3VuZCByb3VuZGVkLWxnIG1kOmhpZGRlblwiXG4gICAgICA+XG4gICAgICAgIDxCYXJzM0ljb24gY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICA8L2J1dHRvbj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIlBsdXNJY29uIiwiQ2hhdEJ1YmJsZUxlZnRJY29uIiwiVHJhc2hJY29uIiwiUGVuY2lsSWNvbiIsIlhNYXJrSWNvbiIsIkJhcnMzSWNvbiIsIkNoZXZyb25Eb3duSWNvbiIsIkNoZXZyb25SaWdodEljb24iLCJGb2xkZXJQbHVzSWNvbiIsIkVsbGlwc2lzVmVydGljYWxJY29uIiwiU2V0dGluZ3MiLCJCcmFpbiIsIkZpbGVUZXh0IiwiY24iLCJTeW5jU3RhdHVzIiwidXNlU2V0dGluZ3MiLCJTaWRlYmFyIiwiY29udmVyc2F0aW9ucyIsIndvcmtzcGFjZXMiLCJjdXJyZW50Q29udmVyc2F0aW9uSWQiLCJjdXJyZW50V29ya3NwYWNlSWQiLCJpc09wZW4iLCJvblRvZ2dsZSIsIm9uTmV3Q2hhdCIsIm9uU2VsZWN0Q29udmVyc2F0aW9uIiwib25EZWxldGVDb252ZXJzYXRpb24iLCJvblJlbmFtZUNvbnZlcnNhdGlvbiIsIm9uU2VsZWN0V29ya3NwYWNlIiwib25DcmVhdGVXb3Jrc3BhY2UiLCJvblVwZGF0ZVdvcmtzcGFjZSIsIm9uRGVsZXRlV29ya3NwYWNlIiwib25SZW9yZGVyV29ya3NwYWNlcyIsIm9uQXNzaWduQ29udmVyc2F0aW9uVG9Xb3Jrc3BhY2UiLCJvbk9wZW5TZXR0aW5ncyIsImVkaXRpbmdJZCIsInNldEVkaXRpbmdJZCIsImVkaXRUaXRsZSIsInNldEVkaXRUaXRsZSIsImhvdmVyZWRJZCIsInNldEhvdmVyZWRJZCIsImV4cGFuZGVkV29ya3NwYWNlcyIsInNldEV4cGFuZGVkV29ya3NwYWNlcyIsIlNldCIsInNob3dXb3Jrc3BhY2VGb3JtIiwic2V0U2hvd1dvcmtzcGFjZUZvcm0iLCJlZGl0aW5nV29ya3NwYWNlSWQiLCJzZXRFZGl0aW5nV29ya3NwYWNlSWQiLCJzaG93Q29udmVyc2F0aW9uTWVudSIsInNldFNob3dDb252ZXJzYXRpb25NZW51IiwiZHJhZ2dlZFdvcmtzcGFjZSIsInNldERyYWdnZWRXb3Jrc3BhY2UiLCJkcmFnT3ZlcldvcmtzcGFjZSIsInNldERyYWdPdmVyV29ya3NwYWNlIiwiaG92ZXJlZFdvcmtzcGFjZSIsInNldEhvdmVyZWRXb3Jrc3BhY2UiLCJkcmFnZ2VkQ29udmVyc2F0aW9uIiwic2V0RHJhZ2dlZENvbnZlcnNhdGlvbiIsImRyYWdPdmVyVGFyZ2V0Iiwic2V0RHJhZ092ZXJUYXJnZXQiLCJ3b3Jrc3BhY2VGb3JtRGF0YSIsInNldFdvcmtzcGFjZUZvcm1EYXRhIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiY29sb3IiLCJyb3V0ZXIiLCJzZXR0aW5ncyIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVTdGFydEVkaXQiLCJjb252ZXJzYXRpb24iLCJpZCIsInRpdGxlIiwiaGFuZGxlU2F2ZUVkaXQiLCJ0cmltIiwiaGFuZGxlQ2FuY2VsRWRpdCIsInRvZ2dsZVdvcmtzcGFjZSIsIndvcmtzcGFjZUlkIiwibmV3RXhwYW5kZWQiLCJoYXMiLCJkZWxldGUiLCJhZGQiLCJoYW5kbGVDcmVhdGVXb3Jrc3BhY2UiLCJ1bmRlZmluZWQiLCJoYW5kbGVVcGRhdGVXb3Jrc3BhY2UiLCJ3b3Jrc3BhY2UiLCJmaW5kIiwidyIsInN0YXJ0RWRpdFdvcmtzcGFjZSIsImhhbmRsZURlbGV0ZVdvcmtzcGFjZSIsImNvbmZpcm0iLCJoYW5kbGVDcmVhdGVDb252ZXJzYXRpb25JbldvcmtzcGFjZSIsImhhbmRsZUFzc2lnbkNvbnZlcnNhdGlvbiIsImNvbnZlcnNhdGlvbklkIiwiaGFuZGxlRHJhZ1N0YXJ0IiwiZSIsImRhdGFUcmFuc2ZlciIsImVmZmVjdEFsbG93ZWQiLCJzZXREYXRhIiwiaGFuZGxlRHJhZ092ZXIiLCJwcmV2ZW50RGVmYXVsdCIsInN0b3BQcm9wYWdhdGlvbiIsImRyb3BFZmZlY3QiLCJyZWN0IiwiY3VycmVudFRhcmdldCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsIm1pZFkiLCJ0b3AiLCJoZWlnaHQiLCJpbnNlcnRBYm92ZSIsImNsaWVudFkiLCJkYXRhc2V0IiwidG9TdHJpbmciLCJoYW5kbGVEcmFnTGVhdmUiLCJoYW5kbGVEcmFnRW5kIiwiaGFuZGxlQ29udmVyc2F0aW9uRHJhZ1N0YXJ0IiwiaGFuZGxlV29ya3NwYWNlRHJvcFpvbmUiLCJ0eXBlIiwiaGFuZGxlQ29udmVyc2F0aW9uRHJvcCIsInRhcmdldFdvcmtzcGFjZUlkIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlRHJvcEJldHdlZW4iLCJpbnNlcnRJbmRleCIsImRyYWdnZWRJbmRleCIsImZpbmRJbmRleCIsImxvZyIsInRvdGFsV29ya3NwYWNlcyIsImxlbmd0aCIsIndvcmtzcGFjZUlkcyIsIm1hcCIsImFkanVzdGVkSW5kZXgiLCJuZXdXb3Jrc3BhY2VzIiwiZHJhZ2dlZEl0ZW0iLCJzcGxpY2UiLCJ1bmFzc2lnbmVkQ29udmVyc2F0aW9ucyIsImZpbHRlciIsImNvbnYiLCJ3b3Jrc3BhY2VDb252ZXJzYXRpb25zIiwicmVkdWNlIiwiYWNjIiwiZm9ybWF0RGF0ZSIsInRpbWVzdGFtcCIsImRhdGUiLCJEYXRlIiwibm93IiwiZGlmZkluRGF5cyIsIk1hdGgiLCJmbG9vciIsImdldFRpbWUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwiaDEiLCJidXR0b24iLCJoMyIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicHJldiIsInRhcmdldCIsInNwYW4iLCJvbkRyYWdPdmVyIiwib25EcmFnTGVhdmUiLCJvbkRyb3AiLCJpbmRleCIsImRyYWdnYWJsZSIsIm9uRHJhZ1N0YXJ0Iiwib25EcmFnRW5kIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJvbk1vdXNlRG93biIsIm9uQmx1ciIsIm9uS2V5RG93biIsImtleSIsImF1dG9Gb2N1cyIsInVwZGF0ZWRBdCIsIndzIiwicHVzaCIsInplcFNldHRpbmdzIiwiZW5hYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});