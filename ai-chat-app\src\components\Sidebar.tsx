'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Conversation, Workspace } from '@/types/chat';
import {
  PlusIcon,
  ChatBubbleLeftIcon,
  TrashIcon,
  PencilIcon,
  XMarkIcon,
  Bars3Icon,
  ChevronDownIcon,
  ChevronRightIcon,
  FolderIcon,
  FolderPlusIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { Settings, Brain, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SyncStatus } from './SyncStatus';
import { useSettings } from '@/contexts/SettingsContext';

interface SidebarProps {
  conversations: Conversation[];
  workspaces: Workspace[];
  currentConversationId: string | null;
  currentWorkspaceId: string | null;
  isOpen: boolean;
  onToggle: () => void;
  onNewChat: (workspaceId?: string) => void;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onRenameConversation: (id: string, newTitle: string) => void;
  onSelectWorkspace: (id: string | null) => void;
  onCreateWorkspace: (workspace: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'userId'>) => void;
  onUpdateWorkspace: (workspace: Workspace) => void;
  onDeleteWorkspace: (id: string) => void;
  onReorderWorkspaces: (workspaceIds: string[]) => void;
  onAssignConversationToWorkspace: (conversationId: string, workspaceId: string | null) => void;
  onOpenSettings?: () => void;
}

export default function Sidebar({
  conversations,
  workspaces,
  currentConversationId,
  currentWorkspaceId,
  isOpen,
  onToggle,
  onNewChat,
  onSelectConversation,
  onDeleteConversation,
  onRenameConversation,
  onSelectWorkspace,
  onCreateWorkspace,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onReorderWorkspaces,
  onAssignConversationToWorkspace,
  onOpenSettings
}: SidebarProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const [expandedWorkspaces, setExpandedWorkspaces] = useState<Set<string>>(new Set());
  const [showWorkspaceForm, setShowWorkspaceForm] = useState(false);
  const [editingWorkspaceId, setEditingWorkspaceId] = useState<string | null>(null);
  const [showConversationMenu, setShowConversationMenu] = useState<string | null>(null);
  const [draggedWorkspace, setDraggedWorkspace] = useState<string | null>(null);
  const [dragOverWorkspace, setDragOverWorkspace] = useState<string | null>(null);
  const [hoveredWorkspace, setHoveredWorkspace] = useState<string | null>(null);
  const [draggedConversation, setDraggedConversation] = useState<string | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<{ type: 'workspace' | 'unassigned', id?: string } | null>(null);
  const [workspaceFormData, setWorkspaceFormData] = useState({
    name: '',
    description: '',
    color: '#6366f1'
  });
  const router = useRouter();
  const { settings } = useSettings();

  // Close conversation menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowConversationMenu(null);
    };

    if (showConversationMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showConversationMenu]);

  const handleStartEdit = (conversation: Conversation) => {
    setEditingId(conversation.id);
    setEditTitle(conversation.title);
  };

  const handleSaveEdit = () => {
    if (editingId && editTitle.trim()) {
      onRenameConversation(editingId, editTitle.trim());
    }
    setEditingId(null);
    setEditTitle('');
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditTitle('');
  };

  // Workspace helper functions
  const toggleWorkspace = (workspaceId: string) => {
    const newExpanded = new Set(expandedWorkspaces);
    if (newExpanded.has(workspaceId)) {
      newExpanded.delete(workspaceId);
    } else {
      newExpanded.add(workspaceId);
    }
    setExpandedWorkspaces(newExpanded);
  };

  const handleCreateWorkspace = () => {
    if (workspaceFormData.name.trim()) {
      onCreateWorkspace({
        name: workspaceFormData.name.trim(),
        description: workspaceFormData.description.trim() || undefined,
        color: workspaceFormData.color
      });
      setWorkspaceFormData({ name: '', description: '', color: '#6366f1' });
      setShowWorkspaceForm(false);
    }
  };

  const handleUpdateWorkspace = () => {
    if (editingWorkspaceId && workspaceFormData.name.trim()) {
      const workspace = workspaces.find(w => w.id === editingWorkspaceId);
      if (workspace) {
        onUpdateWorkspace({
          ...workspace,
          name: workspaceFormData.name.trim(),
          description: workspaceFormData.description.trim() || undefined,
          color: workspaceFormData.color
        });
      }
      setEditingWorkspaceId(null);
      setWorkspaceFormData({ name: '', description: '', color: '#6366f1' });
    }
  };

  const startEditWorkspace = (workspace: Workspace) => {
    setEditingWorkspaceId(workspace.id);
    setWorkspaceFormData({
      name: workspace.name,
      description: workspace.description || '',
      color: workspace.color
    });
  };

  const handleDeleteWorkspace = (workspaceId: string) => {
    if (confirm('Are you sure you want to delete this workspace? Conversations will be moved to unassigned.')) {
      onDeleteWorkspace(workspaceId);
    }
  };

  const handleCreateConversationInWorkspace = (workspaceId: string) => {
    // Create a new conversation and assign it to the workspace
    onNewChat(workspaceId);
  };

  const handleAssignConversation = (conversationId: string, workspaceId: string | null) => {
    onAssignConversationToWorkspace(conversationId, workspaceId);
    setShowConversationMenu(null);
  };

  // Drag and drop handlers for workspace reordering
  const handleDragStart = (e: React.DragEvent, workspaceId: string) => {
    setDraggedWorkspace(workspaceId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', workspaceId);
  };

  const handleDragOver = (e: React.DragEvent, workspaceId: string) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';

    if (draggedWorkspace && draggedWorkspace !== workspaceId) {
      // Determine if we should insert above or below based on mouse position
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const midY = rect.top + rect.height / 2;
      const insertAbove = e.clientY < midY;

      setDragOverWorkspace(workspaceId);
      // Store the insert position for visual feedback
      (e.currentTarget as HTMLElement).dataset.insertAbove = insertAbove.toString();
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOverWorkspace(null);
    // Clear the insert position
    (e.currentTarget as HTMLElement).dataset.insertAbove = '';
  };

  const handleDragEnd = () => {
    setDraggedWorkspace(null);
    setDragOverWorkspace(null);
    setDraggedConversation(null);
    setDragOverTarget(null);
  };

  // Conversation drag and drop handlers
  const handleConversationDragStart = (e: React.DragEvent, conversationId: string) => {
    setDraggedConversation(conversationId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', conversationId);
  };

  const handleWorkspaceDropZone = (e: React.DragEvent, workspaceId?: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (draggedConversation) {
      setDragOverTarget({ type: workspaceId ? 'workspace' : 'unassigned', id: workspaceId });
    }
  };

  const handleConversationDrop = async (e: React.DragEvent, targetWorkspaceId?: string) => {
    e.preventDefault();
    e.stopPropagation();

    if (!draggedConversation) return;

    try {
      await onAssignConversationToWorkspace(draggedConversation, targetWorkspaceId || null);
    } catch (error) {
      console.error('Failed to move conversation:', error);
    } finally {
      setDraggedConversation(null);
      setDragOverTarget(null);
    }
  };

  const handleDropBetween = async (insertIndex: number) => {
    if (!draggedWorkspace) {
      setDraggedWorkspace(null);
      setDragOverWorkspace(null);
      return;
    }

    try {
      // Find the index of the dragged workspace
      const draggedIndex = workspaces.findIndex(w => w.id === draggedWorkspace);

      if (draggedIndex === -1) return;

      console.log('Sidebar: handleDropBetween called', {
        draggedWorkspace,
        draggedIndex,
        insertIndex,
        totalWorkspaces: workspaces.length,
        workspaceIds: workspaces.map(w => w.id)
      });

      // Adjust insert index if dragging down
      let adjustedIndex = insertIndex;
      if (draggedIndex < insertIndex) {
        adjustedIndex = insertIndex - 1;
      }

      console.log('Sidebar: Adjusted index:', adjustedIndex);

      // Check if the position is actually changing
      if (draggedIndex === adjustedIndex) {
        console.log('Sidebar: No position change needed, skipping reorder');
        return;
      }

      // Create new order
      const newWorkspaces = [...workspaces];
      const [draggedItem] = newWorkspaces.splice(draggedIndex, 1);
      newWorkspaces.splice(adjustedIndex, 0, draggedItem);

      // Update positions and call reorder API
      const workspaceIds = newWorkspaces.map(w => w.id);

      console.log('Sidebar: Calling reorder with new order:', workspaceIds);
      await onReorderWorkspaces(workspaceIds);
    } catch (error) {
      console.error('Failed to reorder workspaces:', error);
    } finally {
      setDraggedWorkspace(null);
      setDragOverWorkspace(null);
    }
  };

  // Group conversations by workspace
  const unassignedConversations = conversations.filter(conv => !conv.workspaceId);
  const workspaceConversations = workspaces.reduce((acc, workspace) => {
    acc[workspace.id] = conversations.filter(conv => conv.workspaceId === workspace.id);
    return acc;
  }, {} as Record<string, Conversation[]>);




  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed left-0 top-0 h-full bg-sidebar text-sidebar-foreground z-50 transition-transform duration-300 ease-in-out",
        "w-96 md:w-80",
        isOpen ? "translate-x-0" : "-translate-x-full",
        "md:relative md:translate-x-0"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
            <h1 className="text-xl font-bold">Nexus</h1>
            <button
              onClick={onToggle}
              className="p-2 hover:bg-sidebar-accent rounded-lg md:hidden"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* New Chat Button */}
          <div className="p-4">
            <button
              onClick={onNewChat}
              className="w-full flex items-center gap-3 p-3 bg-sidebar-accent hover:bg-sidebar-accent/80 rounded-lg transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              New Chat
            </button>
          </div>

          {/* Workspaces and Conversations */}
          <div className="flex-1 overflow-y-auto px-4 pb-4">
            {/* Workspace Management */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-sidebar-muted">Workspaces</h3>
                <button
                  onClick={() => setShowWorkspaceForm(true)}
                  className="p-1 hover:bg-sidebar-accent rounded-md transition-colors"
                  title="Create workspace"
                >
                  <FolderPlusIcon className="w-4 h-4" />
                </button>
              </div>

              {/* Workspace Creation Form */}
              {showWorkspaceForm && (
                <div className="mb-3 p-3 bg-sidebar-accent/50 rounded-lg">
                  <input
                    type="text"
                    placeholder="Workspace name"
                    value={workspaceFormData.name}
                    onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2"
                  />
                  <input
                    type="text"
                    placeholder="Description (optional)"
                    value={workspaceFormData.description}
                    onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2"
                  />
                  <div className="flex items-center gap-2 mb-3">
                    <input
                      type="color"
                      value={workspaceFormData.color}
                      onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-8 h-8 rounded border border-sidebar-border"
                    />
                    <span className="text-xs text-sidebar-muted">Color</span>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={handleCreateWorkspace}
                      className="px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90"
                    >
                      Create
                    </button>
                    <button
                      onClick={() => {
                        setShowWorkspaceForm(false);
                        setWorkspaceFormData({ name: '', description: '', color: '#6366f1' });
                      }}
                      className="px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              {/* Workspace Edit Form */}
              {editingWorkspaceId && (
                <div className="mb-3 p-3 bg-sidebar-accent/50 rounded-lg">
                  <input
                    type="text"
                    placeholder="Workspace name"
                    value={workspaceFormData.name}
                    onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2"
                  />
                  <input
                    type="text"
                    placeholder="Description (optional)"
                    value={workspaceFormData.description}
                    onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full bg-sidebar text-sidebar-foreground px-3 py-2 rounded-md text-sm mb-2"
                  />
                  <div className="flex items-center gap-2 mb-3">
                    <input
                      type="color"
                      value={workspaceFormData.color}
                      onChange={(e) => setWorkspaceFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-8 h-8 rounded border border-sidebar-border"
                    />
                    <span className="text-xs text-sidebar-muted">Color</span>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={handleUpdateWorkspace}
                      className="px-3 py-1 bg-primary text-primary-foreground rounded-md text-sm hover:bg-primary/90"
                    >
                      Update
                    </button>
                    <button
                      onClick={() => {
                        setEditingWorkspaceId(null);
                        setWorkspaceFormData({ name: '', description: '', color: '#6366f1' });
                      }}
                      className="px-3 py-1 bg-sidebar-accent text-sidebar-foreground rounded-md text-sm hover:bg-sidebar-accent/80"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Workspaces Section */}
            {workspaces.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-sidebar-muted mb-3 px-1">My Workspaces</h3>
                <div className="space-y-2">
                  {/* Drop zone above first workspace */}
                  {draggedWorkspace && (
                    <div
                      className={cn(
                        "h-2 -mb-1 transition-all duration-200",
                        dragOverWorkspace === 'above-first-workspace' && "bg-blue-500 rounded"
                      )}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setDragOverWorkspace('above-first-workspace');
                      }}
                      onDragLeave={() => setDragOverWorkspace(null)}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleDropBetween(0); // Insert at the beginning
                      }}
                    />
                  )}

                  {/* Workspace Items */}
                  {workspaces.map((workspace, index) => (
                    <div key={workspace.id}>
                      {/* Drop zone above workspace (except first one) */}
                      {draggedWorkspace && draggedWorkspace !== workspace.id && index > 0 && (
                        <div
                          className={cn(
                            "h-2 -mb-1 transition-all duration-200",
                            dragOverWorkspace === `above-${workspace.id}` && "bg-blue-500 rounded"
                          )}
                          onDragOver={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setDragOverWorkspace(`above-${workspace.id}`);
                          }}
                          onDragLeave={() => setDragOverWorkspace(null)}
                          onDrop={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDropBetween(index);
                          }}
                        />
                      )}

                      <div
                        className={cn(
                          "group flex items-center gap-2 p-2 rounded-lg transition-all duration-200 relative cursor-move",
                          draggedWorkspace === workspace.id && "opacity-50 scale-95",
                          draggedWorkspace && draggedWorkspace !== workspace.id && "opacity-75"
                        )}
                        draggable={true}
                        onDragStart={(e) => handleDragStart(e, workspace.id)}
                        onDragEnd={handleDragEnd}
                        onMouseEnter={() => setHoveredWorkspace(workspace.id)}
                        onMouseLeave={() => setHoveredWorkspace(null)}
                      >
                        <div
                          className="w-3 h-3 rounded-full flex-shrink-0"
                          style={{ backgroundColor: workspace.color }}
                        />
                        <span className="text-sm font-medium text-sidebar-foreground truncate">
                          {workspace.name}
                        </span>
                        <span className="text-xs text-sidebar-muted">
                          ({workspaceConversations[workspace.id]?.length || 0})
                        </span>
                        <div className="flex gap-1 ml-auto" draggable={false}>
                          <button
                            onClick={() => handleCreateConversationInWorkspace(workspace.id)}
                            className={cn(
                              "p-1 hover:bg-green-500/20 rounded-md text-green-400 hover:text-green-300 transition-all duration-200",
                              hoveredWorkspace === workspace.id ? "opacity-100" : "opacity-0"
                            )}
                            title="New conversation in workspace"
                            draggable={false}
                            onMouseDown={(e) => e.stopPropagation()}
                          >
                            <PlusIcon className="w-3 h-3" />
                          </button>
                          <button
                            onClick={() => startEditWorkspace(workspace)}
                            className={cn(
                              "p-1 hover:bg-sidebar-accent rounded-md transition-all duration-200",
                              hoveredWorkspace === workspace.id ? "opacity-100" : "opacity-0"
                            )}
                            title="Edit workspace"
                            draggable={false}
                            onMouseDown={(e) => e.stopPropagation()}
                          >
                            <PencilIcon className="w-3 h-3" />
                          </button>
                          <button
                            onClick={() => handleDeleteWorkspace(workspace.id)}
                            className={cn(
                              "p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-all duration-200",
                              hoveredWorkspace === workspace.id ? "opacity-100" : "opacity-0"
                            )}
                            title="Delete workspace"
                            draggable={false}
                            onMouseDown={(e) => e.stopPropagation()}
                          >
                            <TrashIcon className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Drop zone below all workspaces */}
                  {draggedWorkspace && (
                    <div
                      className={cn(
                        "h-2 transition-all duration-200",
                        dragOverWorkspace === 'below-all-workspaces' && "bg-blue-500 rounded"
                      )}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setDragOverWorkspace('below-all-workspaces');
                      }}
                      onDragLeave={() => setDragOverWorkspace(null)}
                      onDrop={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleDropBetween(workspaces.length);
                      }}
                    />
                  )}
                </div>
              </div>
            )}

            {/* Conversations Section */}
            {conversations.length === 0 ? (
              <div className="text-sidebar-muted text-center py-8">
                No conversations yet
              </div>
            ) : (
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-sidebar-muted mb-3 px-1">Conversations</h3>

                {/* Unassigned Conversations */}
                {unassignedConversations.length > 0 && (
                  <div>
                    <div
                      className={cn(
                        "flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors",
                        dragOverTarget?.type === 'unassigned' && "bg-green-500/20 border-2 border-green-500 border-dashed"
                      )}
                      onDragOver={(e) => draggedConversation && handleWorkspaceDropZone(e)}
                      onDrop={(e) => draggedConversation && handleConversationDrop(e)}
                    >
                      <ChatBubbleLeftIcon className="w-4 h-4 text-sidebar-muted" />
                      <span className="text-sm font-medium text-sidebar-muted">Unassigned</span>
                      <span className="text-xs text-sidebar-muted">({unassignedConversations.length})</span>
                    </div>
                    <div className="space-y-1 ml-6">
                      {unassignedConversations.map((conversation) => (
                        <div
                          key={conversation.id}
                          className={cn(
                            "group relative p-2 rounded-lg cursor-pointer transition-colors",
                            currentConversationId === conversation.id
                              ? "bg-sidebar-accent"
                              : "hover:bg-sidebar-accent/50",
                            draggedConversation === conversation.id && "opacity-50"
                          )}
                          draggable
                          onDragStart={(e) => handleConversationDragStart(e, conversation.id)}
                          onDragEnd={handleDragEnd}
                          onClick={() => onSelectConversation(conversation.id)}
                          onMouseEnter={() => setHoveredId(conversation.id)}
                          onMouseLeave={() => setHoveredId(null)}
                        >
                          <div className="flex items-start gap-2">
                            <div className="flex-1 min-w-0">
                              {editingId === conversation.id ? (
                                <input
                                  type="text"
                                  value={editTitle}
                                  onChange={(e) => setEditTitle(e.target.value)}
                                  onBlur={handleSaveEdit}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') handleSaveEdit();
                                    if (e.key === 'Escape') handleCancelEdit();
                                  }}
                                  className="w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm"
                                  autoFocus
                                  onClick={(e) => e.stopPropagation()}
                                />
                              ) : (
                                <>
                                  <div className="font-medium text-sm truncate">
                                    {conversation.title}
                                  </div>
                                  <div className="text-xs text-sidebar-muted mt-1">
                                    {formatDate(conversation.updatedAt)}
                                  </div>
                                </>
                              )}
                            </div>

                            {/* Action buttons */}
                            <div className={cn(
                              "flex gap-1 transition-opacity duration-200",
                              hoveredId === conversation.id ? "opacity-100" : "opacity-0"
                            )}>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleStartEdit(conversation);
                                }}
                                className="p-1 hover:bg-sidebar-accent rounded-md transition-colors"
                                title="Edit conversation"
                              >
                                <PencilIcon className="w-3 h-3" />
                              </button>
                              <div className="relative">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setShowConversationMenu(
                                      showConversationMenu === conversation.id ? null : conversation.id
                                    );
                                  }}
                                  className="p-1 hover:bg-sidebar-accent rounded-md transition-colors"
                                  title="Move to workspace"
                                >
                                  <EllipsisVerticalIcon className="w-3 h-3" />
                                </button>

                                {/* Workspace assignment dropdown */}
                                {showConversationMenu === conversation.id && (
                                  <div className="absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]">
                                    <div className="py-1">
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleAssignConversation(conversation.id, null);
                                        }}
                                        className="w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors"
                                      >
                                        📝 Unassigned
                                      </button>
                                      {workspaces.map((ws) => (
                                        <button
                                          key={ws.id}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleAssignConversation(conversation.id, ws.id);
                                          }}
                                          className="w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2"
                                        >
                                          <div
                                            className="w-2 h-2 rounded-full flex-shrink-0"
                                            style={{ backgroundColor: ws.color }}
                                          />
                                          {ws.name}
                                        </button>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDeleteConversation(conversation.id);
                                }}
                                className="p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors"
                                title="Delete conversation"
                              >
                                <TrashIcon className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Workspace Conversations */}
                {workspaces.map((workspace) => (
                  <div key={workspace.id}>
                    <div
                      className={cn(
                        "flex items-center gap-2 mb-2 p-2 rounded-lg transition-colors",
                        dragOverTarget?.type === 'workspace' && dragOverTarget.id === workspace.id && "bg-green-500/20 border-2 border-green-500 border-dashed"
                      )}
                      onDragOver={(e) => {
                        if (draggedConversation) {
                          handleWorkspaceDropZone(e, workspace.id);
                        }
                      }}
                      onDragLeave={(e) => {
                        if (draggedConversation) {
                          setDragOverTarget(null);
                        }
                      }}
                      onDrop={(e) => {
                        if (draggedConversation) {
                          handleConversationDrop(e, workspace.id);
                        }
                      }}
                    >
                      <button
                        onClick={() => toggleWorkspace(workspace.id)}
                        className="p-0.5 hover:bg-sidebar-accent rounded transition-colors"
                      >
                        {expandedWorkspaces.has(workspace.id) ? (
                          <ChevronDownIcon className="w-4 h-4 text-sidebar-muted" />
                        ) : (
                          <ChevronRightIcon className="w-4 h-4 text-sidebar-muted" />
                        )}
                      </button>
                      <div
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: workspace.color }}
                      />
                      <span className="text-sm font-medium text-sidebar-foreground truncate">
                        {workspace.name}
                      </span>
                      <span className="text-xs text-sidebar-muted">
                        ({workspaceConversations[workspace.id]?.length || 0})
                      </span>
                    </div>

                    {expandedWorkspaces.has(workspace.id) && workspaceConversations[workspace.id] && (
                      <div className="space-y-1 ml-6 mb-2">
                        {workspaceConversations[workspace.id].map((conversation) => (
                          <div
                            key={conversation.id}
                            className={cn(
                              "group relative p-2 rounded-lg cursor-pointer transition-colors",
                              currentConversationId === conversation.id
                                ? "bg-sidebar-accent"
                                : "hover:bg-sidebar-accent/50",
                              draggedConversation === conversation.id && "opacity-50"
                            )}
                            draggable
                            onDragStart={(e) => handleConversationDragStart(e, conversation.id)}
                            onDragEnd={handleDragEnd}
                            onClick={() => onSelectConversation(conversation.id)}
                            onMouseEnter={() => setHoveredId(conversation.id)}
                            onMouseLeave={() => setHoveredId(null)}
                          >
                            <div className="flex items-start gap-2">
                              <div className="flex-1 min-w-0">
                                {editingId === conversation.id ? (
                                  <input
                                    type="text"
                                    value={editTitle}
                                    onChange={(e) => setEditTitle(e.target.value)}
                                    onBlur={handleSaveEdit}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') handleSaveEdit();
                                      if (e.key === 'Escape') handleCancelEdit();
                                    }}
                                    className="w-full bg-sidebar-accent text-sidebar-foreground px-2 py-1 rounded text-sm"
                                    autoFocus
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                ) : (
                                  <>
                                    <div className="font-medium text-sm truncate">
                                      {conversation.title}
                                    </div>
                                    <div className="text-xs text-sidebar-muted mt-1">
                                      {formatDate(conversation.updatedAt)}
                                    </div>
                                  </>
                                )}
                              </div>

                              {/* Action buttons */}
                              <div className={cn(
                                "flex gap-1 transition-opacity duration-200",
                                hoveredId === conversation.id ? "opacity-100" : "opacity-0"
                              )}>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStartEdit(conversation);
                                  }}
                                  className="p-1 hover:bg-sidebar-accent rounded-md transition-colors"
                                  title="Edit conversation"
                                >
                                  <PencilIcon className="w-3 h-3" />
                                </button>
                                <div className="relative">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setShowConversationMenu(
                                        showConversationMenu === conversation.id ? null : conversation.id
                                      );
                                    }}
                                    className="p-1 hover:bg-sidebar-accent rounded-md transition-colors"
                                    title="Move to workspace"
                                  >
                                    <EllipsisVerticalIcon className="w-3 h-3" />
                                  </button>

                                  {/* Workspace assignment dropdown */}
                                  {showConversationMenu === conversation.id && (
                                    <div className="absolute right-0 top-full mt-1 bg-sidebar border border-sidebar-border rounded-md shadow-lg z-50 min-w-[160px]">
                                      <div className="py-1">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleAssignConversation(conversation.id, null);
                                          }}
                                          className="w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors"
                                        >
                                          📝 Unassigned
                                        </button>
                                        {workspaces.map((ws) => (
                                          <button
                                            key={ws.id}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleAssignConversation(conversation.id, ws.id);
                                            }}
                                            className="w-full text-left px-3 py-2 text-sm hover:bg-sidebar-accent transition-colors flex items-center gap-2"
                                          >
                                            <div
                                              className="w-2 h-2 rounded-full flex-shrink-0"
                                              style={{ backgroundColor: ws.color }}
                                            />
                                            {ws.name}
                                          </button>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onDeleteConversation(conversation.id);
                                  }}
                                  className="p-1 hover:bg-red-500/20 rounded-md text-red-400 hover:text-red-300 transition-colors"
                                  title="Delete conversation"
                                >
                                  <TrashIcon className="w-3 h-3" />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Bottom Toolbar */}
          <div className="flex-shrink-0 p-3 border-t border-sidebar-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <button
                  onClick={() => router.push('/files')}
                  className="p-2 hover:bg-sidebar-accent rounded-lg transition-colors"
                  title="File Manager"
                >
                  <FileText className="w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground" />
                </button>
                {settings.zepSettings.enabled && (
                  <button
                    onClick={() => router.push('/memory')}
                    className="p-2 hover:bg-sidebar-accent rounded-lg transition-colors"
                    title="Memory Management"
                  >
                    <Brain className="w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground" />
                  </button>
                )}
                <button
                  onClick={onOpenSettings}
                  className="p-2 hover:bg-sidebar-accent rounded-lg transition-colors"
                  title="Settings"
                >
                  <Settings className="w-4 h-4 text-sidebar-muted hover:text-sidebar-foreground" />
                </button>
              </div>

              {/* Status indicator - only show when there's an issue */}
              <SyncStatus className="text-xs" />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile toggle button */}
      <button
        onClick={onToggle}
        className="fixed top-4 left-4 z-30 p-2 bg-sidebar text-sidebar-foreground rounded-lg md:hidden"
      >
        <Bars3Icon className="w-6 h-6" />
      </button>
    </>
  );
}
