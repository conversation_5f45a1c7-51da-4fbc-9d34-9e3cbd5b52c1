{"/api/file-watcher/route": "app/api/file-watcher/route.js", "/api/storage/conversations/route": "app/api/storage/conversations/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/storage/workspaces/route": "app/api/storage/workspaces/route.js", "/api/enhanced-chat/route": "app/api/enhanced-chat/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/storage/current-conversation/route": "app/api/storage/current-conversation/route.js", "/tool-demo/page": "app/tool-demo/page.js", "/page": "app/page.js"}